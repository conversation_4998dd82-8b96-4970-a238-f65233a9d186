# Apache configuration file (see httpd.apache.org/docs/current/mod/quickreference.html)

# PHP settings
#php_flag magic_quotes_gpc off 
#php_flag register_globals off

# disable directory listing
#Options -Indexes

# vypnuti gzip
#SetEnv no-gzip 1

# dalsi zpusob vypnuti gzip
#RewriteRule ^(.*)$ $1 [NS,E=no-gzip:1,E=dont-vary:1]

# zapnuti brotli
#SetOutputFilter BROTLI_COMPRESS
#SetEnvIfNoCase Request_URI \.(?:gif|jpe?g|png)$ no-brotli

CGIPassAuth On
<IfModule mod_setenvif.c>
	# f**k you apache (https://support.deskpro.com/en/kb/articles/missing-authorization-headers-with-apache)
	SetEnvIf Authorization "(.*)" HTTP_AUTHORIZATION=$1
</IfModule>

# enable cool URL
<IfModule mod_rewrite.c>
	RewriteEngine On
#	RewriteBase /

#	RewriteCond %{HTTPS} off
#	RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

	# prevents files starting with dot to be viewed by browser
	RewriteRule /\.|^\. - [F]

#	RewriteRule en/$ /en [R=301,QSA,L]

    # docasna domena
	RewriteCond %{HTTP_HOST} ^preciosa-prod.www6.superkoderi.cz$
	RewriteRule ^(.*)$ https://www.preciosalighting.com/$1  [R=301,L]

	#presmerovani na www
	RewriteCond %{HTTP_HOST} ^preciosalighting.com
	RewriteRule (.*) https://www.preciosalighting.com/$1 [R=301,QSA,L]

    #odstraneni lomitka na konci
	RewriteCond %{REQUEST_URI} !/superadmin/.*
	RewriteCond %{REQUEST_URI} ^(.+)/$
    RewriteRule (.*)/$ /$1 [R=301,QSA,L]

    # odstraneni ?
    RewriteCond %{THE_REQUEST} ^[^\s]+\s+[^?]*?\?
    RewriteCond %{QUERY_STRING} =""
    # For any version of Apache:
    RewriteRule .? %{REQUEST_URI}? [R=301,L]
    # For Apache 2.4+:
    # RewriteRule .? %{REQUEST_URI} [R=301,L,QSD]

	# index.php
	RewriteCond %{THE_REQUEST} ^[A-Z]{3,9}\ /index\.php
    RewriteRule ^index\.php$ / [L,R=301]

	RewriteBase /

	# front controller
	RewriteCond %{REQUEST_FILENAME} !-f
	RewriteCond %{REQUEST_FILENAME} !-d
	RewriteRule !\.(pdf|js|ico|gif|jpg|png|webp|css|rar|zip|tar\.gz)$ index.php [L]
</IfModule>

# enable gzip compression
<IfModule mod_deflate.c>
	AddOutputFilterByType DEFLATE text/html application/x-javascript text/css application/javascript text/javascript text/plain text/xml application/json application/vnd.ms-fontobject application/x-font-opentype application/x-font-truetype application/x-font-ttf application/xml font/eot font/opentype font/otf image/svg+xml image/vnd.microsoft.icon
	SetEnvIfNoCase Request_URI .(mp4|ogv|webm)$ no-gzip dont-vary
</IfModule>

#
#<IfModule mod_deflate.c>
#   <FilesMatch "\.(js|css|ico|txt|htm|html|php|ttf|woff|eot|svg)$">
#   SetOutputFilter DEFLATE
#   </FilesMatch>
#</IfModule>

# Speed up caching
FileETag MTime Size

# Expires
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresDefault "access plus 366 days"
    #ExpiresDefault "access plus 300 seconds"

    # Future Expires Headers
    <FilesMatch "\.(ico|pdf|flv|jpg|jpeg|png|webp|gif|js|css|swf)$">
    ExpiresDefault A2592000
    </FilesMatch>
</IfModule>
