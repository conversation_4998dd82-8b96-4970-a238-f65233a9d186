{"name": "preciosa2022", "version": "1.0.0", "sideEffects": ["**/svg4everybody.js"], "browserslist": ["last 3 versions", "not < 0.01%", "not ie <= 10", "since 2019", "not BlackBerry > 7", "not QQAndroid > 1", "Android > 4.4.1"], "engines": {"node": ">=12.6.0", "npm": ">=6.9.0"}, "devDependencies": {"@babel/core": "7.8.7", "@babel/plugin-proposal-class-properties": "7.14.5", "@babel/plugin-transform-runtime": "7.8.3", "@babel/preset-env": "7.8.7", "@superkoders/eslint-config": "1.2.24", "@superkoders/prettier-config": "0.2.5", "@superkoders/stylelint-config": "2.1.0", "ansi-colors": "4.1.1", "autoprefixer": "^9.6.1", "babel-loader": "^8.0.6", "babel-loader-exclude-node-modules-except": "1.0.3", "browser-sync": "2.26.7", "cheerio": "1.0.0-rc.3", "dateformat": "^3.0.3", "deepmerge": "4.0.0", "del": "5.1.0", "eslint": "6.5.0", "fancy-log": "1.3.3", "gulp": "4.0.2", "gulp-cheerio": "^0.6.3", "gulp-consolidate": "^0.2.0", "gulp-html-beautify": "1.0.1", "gulp-iconfont": "10.0.3", "gulp-imagemin": "6.1.0", "gulp-notify": "^3.2.0", "gulp-plumber": "^1.2.1", "gulp-postcss": "^8.0.0", "gulp-rename": "^1.4.0", "gulp-sass": "^4.0.2", "gulp-sass-vars": "^1.3.0", "gulp-svgmin": "^2.2.0", "gulp-svgstore": "^7.0.1", "gulp-twing": "3.0.1", "gulp-w3cjs": "^1.3.2", "gulp-zip": "^5.0.0", "gulp.spritesmith": "^6.11.0", "husky": "4.2.3", "import-fresh": "3.1.0", "lint-staged": "10.0.8", "node-notifier": "6.0.0", "node-sass-glob-importer": "5.3.2", "postcss": "8.4.5", "stylelint": "14.0.0", "through2": "^3.0.1", "twing": "3.1.1", "vinyl": "2.2.0", "webpack": "4.41.0"}, "dependencies": {"@superkoders/cookie": "2.4.1", "@superkoders/modal": "1.7.0", "@superkoders/sk-tools": "1.8.1", "@vimeo/player": "2.16.3", "embla-carousel": "8.0.0-rc07", "embla-carousel-autoplay": "8.2.1", "libphonenumber-js": "1.8.5", "lite-vimeo-embed": "0.2.0", "lite-youtube-embed": "0.3.0", "naja": "2.1.5", "nette-forms": "3.3.1", "nouislider": "15.2.0", "scroll-lock": "2.1.5", "stimulus": "3.0.1", "stimulus-use": "0.41.0", "svg4everybody": "^2.1.9", "wnumb": "1.2.0", "yt-player": "3.5.0"}, "scripts": {"changelog": "conventional-changelog -p angular -i CHANGELOG.md -s -r 1", "preversion": "npm run build", "version": "npm run changelog && git add -A", "export": "npx gulp export", "build": "npx gulp min", "dev": "npx gulp", "prestart": "npm install", "preinstall": "npx check-engine", "start": "npm run dev", "lint:css": "stylelint \"src/**/*.{css,scss}\" || exit 0", "lint:css:fix": "prettier-stylelint --write \"src/**/*.{css,scss}\"", "lint:js": "eslint .; exit 0", "lint:js:fix": "eslint . --fix", "installAll": "npm install && cd www/admin/new && npm install", "buildAll": "npm run build && cd www/admin/new && npm run build"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{scss}": ["stylelint --syntax scss \"*.{css,scss}\""], "*.js": ["eslint"]}, "volta": {"node": "12.6.0", "npm": "6.9.0"}}