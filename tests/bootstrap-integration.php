<?php

declare(strict_types=1);

namespace App\Tests;

use App\Tests\Helpers\ContainerFactory;
use Nette\Configurator;
use Nette\DI\Container;

require __DIR__ . '/bootstrap.php';

define('WWW_DIR', ROOT_DIR . '/www');
define('APP_DIR', ROOT_DIR . '/app');
define('FE_TEMPLATE_DIR', APP_DIR . '/FrontModule/templates');
define('RS_TEMPLATE_DIR', APP_DIR . '/AdminModule/templates');
define('IMAGES_DIR', '/data/images/');
define('FILES_DIR', '/data/files/');
define('CACHE_DIR', TEMP_DIR . '/cache');
define('LOG_DIR', __DIR__ . '/nettelog');
define('IS_CLI', true);

return new ContainerFactory(
	function (string|array $additionalConfig = []): Container {
		$configurator = new Configurator();
		$configurator->setTimeZone('Europe/Prague');
		$configurator->setTempDirectory(TEMP_DIR);
		$configurator->setDebugMode(false);

		\error_reporting(~E_USER_DEPRECATED);

		$configurator->addParameters([
			'appDir' => APP_DIR,
			'wwwDir' => WWW_DIR,
		]);

		$configurator->enableTracy(LOG_DIR);

		$configurator->addParameters(['config' => [
			'webVersion' => 'DEV',
			'APP_DIR' => APP_DIR,
			'IS_CLI' => IS_CLI,
			'FE_TEMPLATE_DIR' => FE_TEMPLATE_DIR,
			'RS_TEMPLATE_DIR' => RS_TEMPLATE_DIR,
			'IMAGES_DIR' => IMAGES_DIR,
			'FILES_DIR' => FILES_DIR,
			'TEMP_DIR' => TEMP_DIR,
			'CACHE_DIR' => CACHE_DIR,
			'WWW_DIR' => WWW_DIR,
			'ROOT_DIR' => ROOT_DIR,
		]]);

		$configurator->addConfig(APP_DIR . '/config/config.neon');
		$configurator->addConfig(__DIR__ . '/config.tests.neon');
		if (file_exists(APP_DIR . '/config/config.local.neon')) {
			$configurator->addConfig(APP_DIR . '/config/config.local.neon');
		}

		$configurator->addConfig($additionalConfig);

		return $configurator->createContainer();
	},
);
