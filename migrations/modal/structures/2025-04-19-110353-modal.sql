-- Export<PERSON><PERSON><PERSON> struktury pro tabulka superadmin_preciosa.modal
DROP TABLE IF EXISTS `modal`;
CREATE TABLE IF NOT EXISTS `modal` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`internalName` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '',
	`customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE
	) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

-- Exportování struktury pro tabulka superadmin_preciosa.modal_localization
DROP TABLE IF EXISTS `modal_localization`;
CREATE TABLE IF NOT EXISTS `modal_localization` (
	`id` int(11) NOT NULL AUTO_INCREMENT,
	`modalId` int(11) NOT NULL,
	`mutationId` int(11) NOT NULL,
	`name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`nameAnchor` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`nameTitle` varchar(250) COLLATE utf8mb4_unicode_520_ci DEFAULT NULL,
	`description` text COLLATE utf8mb4_unicode_520_ci,
	`keywords` text COLLATE utf8mb4_unicode_520_ci,
	`public` int(11) DEFAULT NULL,
	`publicFrom` datetime DEFAULT NULL,
	`publicTo` datetime DEFAULT NULL,
	`isMain` int(11) NOT NULL DEFAULT '0',
	`edited` int(11) DEFAULT NULL,
	`editedTime` datetime DEFAULT NULL,
	`customFieldsJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	`customContentJson` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci,
	PRIMARY KEY (`id`) USING BTREE,
	KEY `FK_modal_localization_mutation` (`mutationId`) USING BTREE,
	KEY `FK_modal_localization_modal` (`modalId`) USING BTREE,
	CONSTRAINT `FK_modal_mutation` FOREIGN KEY (`mutationId`) REFERENCES `mutation` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	CONSTRAINT `FK_modal_modal_parent` FOREIGN KEY (`modalId`) REFERENCES `modal` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
	) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;

ALTER TABLE `modal_localization` ADD COLUMN `hide` INT(11) NOT NULL DEFAULT '0' AFTER `public`;

CREATE TABLE `modal_x_state` (
								  `modalId` int(11) NOT NULL,
								  `stateId` int(11) NOT NULL
);

ALTER TABLE `modal_x_state`
	ADD UNIQUE KEY `modalId_stateId` (`modalId`,`stateId`) USING BTREE,
	ADD KEY `FK_modal_state_state` (`stateId`) USING BTREE;

ALTER TABLE `modal_x_state`
	ADD CONSTRAINT `FK_modal_state_modal` FOREIGN KEY (`modalId`) REFERENCES `modal` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	ADD CONSTRAINT `FK_modal_state_state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;
