
CREATE TABLE `reference_x_reference` (
							   `id` int(11) NOT NULL AUTO_INCREMENT,
							   `referenceId` int(11) NOT NULL,
							   `attachedReferenceId` int(11) NOT NULL,
							   PRIMARY KEY (`id`),
							   <PERSON><PERSON><PERSON> `attachedReferenceId` (`attachedReferenceId`),
							   <PERSON><PERSON>Y `referenceId` (`referenceId`),
							   CONSTRAINT `reference_parent_reference_parent_ibfk_2` FOREIGN KEY (`attachedReferenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
							   CONSTRAINT `reference_parent_reference_parent_ibfk_3` FOREIGN KEY (`referenceId`) REFERENCES `reference` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_czech_ci;
