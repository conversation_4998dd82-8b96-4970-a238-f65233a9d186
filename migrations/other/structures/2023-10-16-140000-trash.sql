CREATE TABLE `trash` (
		 `id` INT(11) NOT NULL AUTO_INCREMENT,
		 `class` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
		 `primaryKey` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
		 `internalName` TEXT NOT NULL COLLATE 'utf8mb4_unicode_520_ci',
		 `userName` VARCHAR(255) NOT NULL DEFAULT '' COLLATE 'utf8mb4_unicode_520_ci',
		 `userId` INT(11) NULL DEFAULT NULL,
		 PRIMARY KEY (`id`) USING BTREE,
		 UNIQUE INDEX `class_primaryKey` (`class`, `primaryKey`) USING BTREE,
		 INDEX `FK_trash_user` (`userId`) USING BTREE,
		 CONSTRAINT `FK_trash_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON UPDATE CASCADE ON DELETE SET NULL
)
	COLLATE='utf8mb4_unicode_520_ci'
ENGINE=InnoDB
;


ALTER TABLE `tree`
	ADD COLUMN `hide` INT(11) NOT NULL DEFAULT '0' AFTER `public`;


ALTER TABLE `trash`
	ADD COLUMN `createdAt` DATETIME NOT NULL AFTER `userId`;
