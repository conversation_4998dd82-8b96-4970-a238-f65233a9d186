START TRANSACTION;

CREATE TABLE `person_x_state` (
	`personId` int(11) NOT NULL,
	`stateId` int(11) NOT NULL
);

ALTER TABLE `person_x_state`
	ADD UNIQUE KEY `personId_stateId` (`personId`,`stateId`) USING BTREE,
	ADD KEY `FK_person_state_state` (`stateId`) USING BTREE;

ALTER TABLE `person_x_state`
	ADD CONSTRAINT `FK_person_state_person` FOREIGN KEY (`personId`) REFERENCES `person` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
	ADD CONSTRAINT `FK_person_state_state` FOREIGN KEY (`stateId`) REFERENCES `state` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;

COMMIT;
