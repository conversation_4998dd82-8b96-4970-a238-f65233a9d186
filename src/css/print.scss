/* stylelint-disable declaration-no-important */
@import 'base/variables';
// @import 'base/mixins';
// @import 'base/extends';

// Setting margins
@page {
	margin: 2cm;
}

html,
body {
	width: 100%;
	background: none !important;
}
* {
	// background: none !important; // optional
	// color: #000000 !important; // optional: convert everything to BW, save print colors
	text-shadow: none !important;
	box-shadow: none !important;
	-webkit-print-color-adjust: exact;
	/* Images, vectors and such */
	// filter: gray(); // optional: convert everything to BW, save print colors
	// filter: grayscale(100%); // optional: convert everything to BW, save print colors
}
a {
	text-decoration: underline !important; // optional
}

// print URL next to the link. Optional
a[href^='http']::after {
	content: ' (' attr(href) ')';
}
// svg {
// 	fill: #000000 !important;
// }
img {
	max-width: 100%;
	height: auto;
}

// Defining all page breaks
h1,
h2,
h3,
h4,
h5,
h6 {
	page-break-after: avoid;
	page-break-inside: avoid;
}
a,
table,
pre,
blockquote,
img,
svg,
figure,
.b-features__item,
.b-story__item,
.b-designer__wrapper,
.b-article,
.grid-highlight__item,
.c-tags,
.footer,
.b-map,
.b-reference,
.b-product,
.b-author,
.b-download,
.b-file,
.b-event,
.b-described-images__item,
.b-designer,
.b-people,
.c-offices__item,
.c-icons__inner,
.b-citation,
.b-description-list__item,
.b-materials__inner,
.b-timeline__item,
.b-app,
.b-design-info__item,
.b-design-step__content,
.b-design-step__wrapper,
.b-details__item,
.b-workflow__item {
	page-break-inside: avoid;
}
ul,
ol,
dl {
	page-break-before: avoid;
}

.title,
.subtitle {
	page-break-after: avoid;
}

// Hide redundants
nav,
form,
iframe,
.u-no-print, // Util class for clients
.btn,
.header__burger,
.header__holder,
.b-intro__down,
.c-articles .c-btns,
.b-background__bg,
.footer__group--menu1,
.footer__group--menu2,
.footer__group--menu3,
.footer__group--bottom,
.b-contact-bg,
.favorite,
.b-filter,
.b-intro-reference__group--btns,
.video,
.embla__btns,
.embla__navigation,
.tabs__navigation,
.b-share,
.btn-wrap,
.b-technology__bg,
.b-designer__btn,
.b-videos__navigation,
.b-sections__menu,
.c-btns,
.b-contact-person,
.b-intro-videos,
.b-intro-press__contact::before,
.b-social,
.b-cta,
.b-map__title-wrapper::before,
.b-color-tabs__dots,
.b-photogallery::before,
.b-content-photo__img-wrap::before,
.b-citation__inner::before,
.b-section__inner::before,
.b-accordion__toggle::before,
.b-workflow::before,
.b-workflow::after,
.b-details__toggle::after,
.b-design-info::before,
.b-design-info__list::before,
.b-composition-tabs__navigation {
	display: none !important;
}

// Bg
.b-intro,
.b-background,
.footer,
.u-bg-default,
.u-bg-graylight,
.b-intro-product,
.b-intro-landing,
.b-described-images__list::before,
.b-described-images__item::before,
.b-contact-person__inner::before,
.b-contact__wrapper,
.b-possibilities__inner::before,
.b-technology,
.b-section__content,
.c-authors-list,
.b-contact-store,
.b-accordion__toggle,
.b-story__nav,
.b-details__box,
.b-intro-designer {
	background: none !important;
}

// Components
.b-intro {
	.row-main {
		min-height: 0;
	}
	&__subtitle,
	&__content {
		margin-bottom: 0;
	}
}
.b-background,
.footer,
.b-described-images__list,
.b-described-images__item,
.b-contact-person__inner,
.b-contact__wrapper,
.b-possibilities__inner,
.b-technology,
.b-section__inner,
.c-authors-list,
.b-intro-press__contact,
.c-articles,
.b-reference-bg,
.c-promo,
.b-development,
.b-accordion__toggle,
.b-project-highlight__content,
.b-materials,
.b-design-info,
.u-bg-default {
	padding: 0 !important;
}
.footer__group--contact,
.b-described-images__item,
.b-contact-person__person {
	margin: 0 !important;
}
.b-intro-product,
.b-intro-landing,
.b-intro-reference,
.b-intro-default,
.b-section__content,
.b-contact-store__content {
	padding-bottom: 0;
}
.b-images__list,
.b-videos__list,
.embla__container {
	flex-wrap: wrap;
	transform: none !important;
	.embla__slide {
		left: auto !important;
		opacity: 1 !important;
	}
}
.b-intro-references,
.b-intro-default__media {
	.img {
		min-width: 100%;
		max-height: 400px;
	}
}
.b-intro-landing {
	.grid {
		flex-direction: row-reverse;
	}
	.grid__cell {
		flex: 0 0 50%;
	}
}
.b-img {
	&__img {
		height: auto;
	}
}
.b-story {
	&__img .img {
		max-height: 300px;
	}
}
.b-details {
	&__img {
		max-height: 500px;
	}
}
.b-images {
	&__list {
		box-sizing: border-box;
	}
	&__item {
		width: 50%;
	}
}
.b-videos {
	&__list {
		box-sizing: border-box;
	}
	&__item {
		width: 50%;
	}
	&__img {
		margin-bottom: 24px;
	}
}
.b-features {
	&__list {
		box-sizing: border-box;
	}
	&__item {
		width: 33.3%;
	}
	&__item--highlight {
		width: 100%;
	}
}
.b-store {
	&__img {
		min-width: 100%;
		max-height: 400px;
	}
}
.b-intro-product {
	.grid {
		flex-direction: row-reverse;
	}
	.grid__cell {
		flex: 0 0 50%;
	}
}
.b-description-list {
	&__item::before {
		content: none;
	}
}
.header,
.footer,
.b-features,
.b-intro-press__contact,
.b-map__title-wrapper,
.b-technology,
.b-intro-designer {
	&,
	* {
		color: black;
	}
}
.footer {
	&__top {
		margin-bottom: 40px;
	}
}
.grid-highlight {
	&__list {
		margin-bottom: 32px;
	}
}
.b-intro {
	padding: 50px;
	&:first-child {
		margin-top: 0;
	}
}
.b-intro-press {
	&__media .img {
		min-width: 100%;
		max-height: 400px;
	}
	&__contact {
		min-height: 0;
	}
}
.b-events {
	padding: 0;
}
.b-intro-event {
	padding-top: 50px;
	&:first-child {
		margin-top: 0;
	}
}
.b-contact-store {
	&__img {
		min-width: 100%;
		max-height: 400px;
	}
}
.b-map__title-wrapper {
	margin: 0;
}
.b-interiors {
	.grid {
		margin: 0 0 -24px -24px;
	}
	.grid__cell {
		width: 50%;
		border-width: 0 0 24px 24px;
	}
	&__img {
		min-width: 100%;
		max-height: 400px;
	}
}
.b-intro-designer {
	margin-top: 0 !important;
	padding: 20px 0 0;
}
.b-intro-designer,
.b-intro-person,
.b-intro-default,
.b-intro-contact,
.b-intro-reference {
	.grid {
		flex-direction: row-reverse;
		align-items: center;
		margin-bottom: 0;
	}
	.grid__cell {
		flex: 0 0 50%;
	}
}
.c-design-steps {
	&__item::before {
		content: none;
	}
	&__item:nth-child(even) .b-design-step__wrapper {
		flex-direction: row;
	}
}
.b-design-step {
	&__wrapper {
		flex-direction: row-reverse;
		flex-wrap: wrap;
		padding: 0;
		&::before {
			content: none;
		}
	}
	&__img {
		flex: 0 0 50%;
		margin: 0;
	}
	&__content {
		flex: 0 0 50%;
	}
	&__sublist-wrap {
		order: 3;
	}
}
.b-color-tabs {
	.grid {
		border-width: 0 0 24px 24px;
	}
	.grid__cell {
		width: 50%;
	}
	&__img {
		height: auto;
		&::before {
			content: '';
			display: block;
			padding-top: percentage(2/3);
		}
		img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}
	&:not(&--product) &__fragment {
		display: block;
	}
}
.b-citation {
	&--highlight &__inner {
		padding-top: 0;
		padding-bottom: 0;
	}
}
.c-icons {
	&__item {
		padding: 0 12px;
	}
}
.b-accordion {
	&__content[hidden] {
		position: static;
		opacity: 1;
	}
}
.b-project-highlight {
	&__item {
		width: 50%;
	}
}
.b-technology-craft {
	.grid__cell {
		width: 50%;
	}
	&__img {
		width: 100%;
	}
}
.b-img-main {
	&__img {
		min-width: 100%;
		max-height: 400px;
	}
}
.b-workflow {
	padding: 0;
	border: none;
}
.b-details {
	&__text[hidden] {
		display: block;
	}
}
.b-composition-tabs {
	&__fragment {
		display: block;
	}
	.grid {
		margin-bottom: 0;
	}
	.grid__cell {
		width: 33.33%;
	}
	&__img {
		width: 100%;
	}
}
.b-design-info {
	&__img {
		min-width: 100%;
		max-height: 400px;
	}
	&__item::before {
		border-color: gray;
	}
}
