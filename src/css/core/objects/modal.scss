.b-modal {
	$s: &;
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	z-index: -1;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: $spacing4 $rowMainGutterSm;
	// background: rgba($colorDark, 0.75);
	visibility: hidden;
	opacity: 0;
	transition: opacity 0.3s, z-index 0s 0.3s, visibility 0s 0.3s;
	&__wrapper {
		display: grid;
		grid-template-columns: auto 1fr auto;
		grid-template-rows: auto 1fr auto auto auto;
		grid-template-areas:
			'header header header'
			'content content content'
			'title title title'
			'desc desc desc'
			'prev nav next';
		width: 100%;
		height: 100%;
		background: $colorWhite;
	}
	&__header {
		position: relative;
		z-index: 11;
		display: flex;
		grid-area: header;
		justify-content: flex-end;
	}
	// &__title {
	// 	z-index: 2;
	// 	grid-area: title;
	// 	color: #ffffff;
	// 	text-align: center;
	// }
	// &__description {
	// 	z-index: 2;
	// 	grid-area: desc;
	// 	color: #ffffff;
	// 	text-align: center;
	// }
	&__prev {
		z-index: 3;
		grid-area: prev;
		// grid-area: 1 / 1 / 4 / 2;
	}
	&__next {
		z-index: 3;
		grid-area: next;
		// grid-area: 1 / 3 / 4 / 4;
	}
	&__content {
		position: relative;
		z-index: 2;
		// grid-area: 1 / 1 / 4 / 4;
		grid-area: content;
		min-height: 140px;
		overflow: hidden;
	}
	&__slide {
		position: absolute;
		top: 0;
		left: 0;
		z-index: 1;
		display: flex;
		align-items: center;
		width: 100%;
		height: 100%;
		overflow: hidden;
		overflow-y: auto;
		opacity: 0;
		transition: opacity 0.3s, z-index 0s 0.3s;
		&.is-active {
			z-index: 2;
			opacity: 1;
			transition: opacity 0.3s, z-index 0s;
		}
	}
	&__image,
	&__video {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		height: 100%;

		> * {
			flex: 0 0 auto;
			width: auto;
			max-width: 100%;
			height: auto;
			max-height: 100%;
		}
		&--multiple {
			> * {
				max-width: 50%;
			}
		}
	}
	&__image img {
		user-select: none;
		object-fit: contain;
	}
	&__inner {
		width: 100%;
		height: 100%; // full height for b-gallery, need to be fixed for other long scrollable pages - whitch is not used currently (min-height doesn't work)
		&:first-child {
			margin-top: auto;
		}
		& > #snippet--contentModal,
		& > #snippet--contentModal > .main {
			height: 100%;
		}
	}
	&__iframe {
		height: 100%;
		padding: 20px;
		background: #ffffff;
		iframe {
			width: 100%;
			height: 100%;
		}
	}
	&__embed {
		width: 100%;
		height: 100%;
		iframe {
			width: 100%;
			height: 100%;
		}
	}
	// &__nav {
	// 	z-index: 2;
	// 	display: flex;
	// 	grid-area: nav;
	// 	justify-content: center;
	// 	align-items: center;
	// 	height: 40px;
	// 	&:empty {
	// 		height: 0;
	// 	}
	// }
	&__nav-item {
		display: block;
		width: 12px;
		height: 12px;
		margin: 0 8px;
		border-radius: 50%;
		background: #eeeeee;
		transition: transform 0.3s;
		cursor: pointer;
		&.is-active {
			transform: scale(1.2);
		}
	}
	&__loader {
		z-index: 10;
		display: none;
		grid-area: 1 / 1 / 6 / 4;
		align-items: center;
		color: #ffffff;
		font-size: 30px;
		justify-items: center;
		& .icon-load {
			width: 40px;
			animation-name: loader;
			animation-duration: 1s;
			animation-timing-function: linear;
			animation-iteration-count: infinite;
		}
	}
	&__loader-icon {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 40px;
		border-radius: 50%;
		background: rgba(0, 0, 0, 0.3);
		animation-name: loader;
		animation-duration: 1s;
		animation-timing-function: linear;
		animation-iteration-count: infinite;
		& > span {
			width: 40px;
			height: 40px;
			line-height: 40px;
			text-align: center;
		}
	}
	&__bg {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: -1;
		// grid-area: 1 / 1 / 6 / 4;
		background-color: rgba($colorDark, 0.75);
	}
	&__prev,
	&__next {
		position: relative;
		display: flex;
		justify-content: center;
		align-items: center;
		// pointer-events: none;
	}
	&__next-btn {
		margin-left: auto;
	}
	&__prev-btn,
	&__next-btn,
	&__close {
		display: flex;
		padding: 17px;
		border: none;
		background: none;
		color: $colorGrayLight;
		outline: none;
		transition: color $t;
		cursor: pointer;
		.icon-svg {
			width: 14px;
			height: 14px;
		}
	}
	&__prev-btn .icon-svg,
	&__next-btn .icon-svg {
		width: 29px;
		height: 10px;
	}

	// Variants
	&--fullscreen {
		top: auto;
		left: auto;
		width: 500px;
		max-width: 100%;
		height: 300px;
	}
	&--fullscreen &__bg {
		background: transparent;
	}
	&--brown &__wrapper {
		background-color: $colorGoldDarker;
	}
	&--gray &__wrapper {
		background-color: $colorGrayDark;
	}

	// STATES
	.hoverevents &__prev-btn:hover,
	.hoverevents &__next-btn:hover,
	.hoverevents &__close:hover {
		color: $colorText;
	}
	&.is-opened {
		z-index: 100;
		visibility: visible;
		opacity: 1;
		transition: opacity 0.3s, z-index 0s, visibility 0s;
	}
	&.is-loading {
		& .b-modal__loader {
			display: grid;
		}
	}
	&.is-first {
		& .b-modal__prev {
			display: none;
		}
	}
	&.is-last {
		& .b-modal__next {
			display: none;
		}
	}

	// MQ
	@media ($mdUp) {
		padding: $spacing9 $rowMainGutter;
		&__prev-btn,
		&__next-btn,
		&__close {
			padding: 29px;
		}
	}
	@media ($mdUp) {
		&__prev-btn,
		&__next-btn,
		&__close {
			padding: 46px;
		}
		&__close .icon-svg {
			width: 19px;
			height: 19px;
		}
	}
}

@keyframes loader {
	from {
		transform: rotate(360deg);
	}
	to {
		transform: rotate(0deg);
	}
}
