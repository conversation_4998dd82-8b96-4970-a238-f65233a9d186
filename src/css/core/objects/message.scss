.message {
	padding: 15px 20px;
	background: $colorBg;
	font-family: $fontSecondary;
	> :last-child {
		margin-bottom: 0;
	}

	// VARIANTs
	&--error,
	&--ok,
	&--warning {
		color: $colorWhite;
		li {
			&::before {
				background-color: $colorWhite;
			}
		}
		a {
			color: $colorWhite;
		}
	}
	&--error {
		background: $colorRed;
	}
	&--ok {
		background: $colorGreen;
	}
	&--warning {
		background: $colorOrange;
	}

	// MQ
	@media ($mdUp) {
		font-size: 14px;
		line-height: (24/14);
	}
}
