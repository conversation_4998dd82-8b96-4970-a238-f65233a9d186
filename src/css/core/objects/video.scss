.video {
	position: relative;
	display: block;
	background-color: $colorBlack;
	overflow: hidden;
	&::before {
		content: '';
		display: block;
		padding-top: 100%;
	}
	&__video {
		position: absolute;
		top: 50%;
		left: 50%;
		width: 101%;
		max-width: initial;
		height: 101%;
		max-height: initial;
		background-position: center center;
		background-repeat: no-repeat;
		background-size: cover;
		transform: translate(-50%, -50%);
		object-fit: cover;
		iframe {
			width: 100%;
			height: 100%;
			background-color: $colorBlack !important;
		}
	}
}
