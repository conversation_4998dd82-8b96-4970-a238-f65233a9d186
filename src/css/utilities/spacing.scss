.u-mb {
	&-last-0 {
		> :last-child:not(.grid) {
			margin-bottom: 0;
		}
	}
	&-0 {
		margin-bottom: 0;
	}
	&-1 {
		margin-bottom: $spacing1;
	}
	&-2 {
		margin-bottom: $spacing2;
	}
	&-3 {
		margin-bottom: $spacing3;
	}
	&-4 {
		margin-bottom: $spacing4;
	}
	&-5 {
		margin-bottom: $spacing5;
	}
	&-6 {
		margin-bottom: $spacing6;
	}
	&-7 {
		margin-bottom: $spacing7;
	}
	&-8 {
		margin-bottom: $spacing8;
	}
	&-9 {
		margin-bottom: $spacing9;
	}
	&-10 {
		margin-bottom: $spacing10;
	}
	&-11 {
		margin-bottom: $spacing11;
	}
	&-12 {
		margin-bottom: $spacing12;
	}
	&-13 {
		margin-bottom: $spacing13;
	}
	&-14 {
		margin-bottom: $spacing14;
	}
	&-15 {
		margin-bottom: $spacing15;
	}
	&-16 {
		margin-bottom: $spacing16;
	}
	&-17 {
		margin-bottom: $spacing17;
	}
	&-18 {
		margin-bottom: $spacing18;
	}
	&-19 {
		margin-bottom: $spacing19;
	}
	&-20 {
		margin-bottom: $spacing20;
	}
	&-21 {
		margin-bottom: $spacing21;
	}
	&-22 {
		margin-bottom: $spacing22;
	}
	&-23 {
		margin-bottom: $spacing23;
	}
	&-24 {
		margin-bottom: $spacing24;
	}
	&-25 {
		margin-bottom: $spacing25;
	}
	&-26 {
		margin-bottom: $spacing26;
	}
	&-27 {
		margin-bottom: $spacing27;
	}
	&-28 {
		margin-bottom: $spacing28;
	}
	&-29 {
		margin-bottom: $spacing29;
	}

	@media ($mdUp), print {
		&-0 {
			@include suffix('md') {
				margin-bottom: 0;
			}
		}
		&-1 {
			@include suffix('md') {
				margin-bottom: $spacing1;
			}
		}
		&-2 {
			@include suffix('md') {
				margin-bottom: $spacing2;
			}
		}
		&-3 {
			@include suffix('md') {
				margin-bottom: $spacing3;
			}
		}
		&-4 {
			@include suffix('md') {
				margin-bottom: $spacing4;
			}
		}
		&-5 {
			@include suffix('md') {
				margin-bottom: $spacing5;
			}
		}
		&-6 {
			@include suffix('md') {
				margin-bottom: $spacing6;
			}
		}
		&-7 {
			@include suffix('md') {
				margin-bottom: $spacing7;
			}
		}
		&-8 {
			@include suffix('md') {
				margin-bottom: $spacing8;
			}
		}
		&-9 {
			@include suffix('md') {
				margin-bottom: $spacing9;
			}
		}
		&-10 {
			@include suffix('md') {
				margin-bottom: $spacing10;
			}
		}
		&-11 {
			@include suffix('md') {
				margin-bottom: $spacing11;
			}
		}
		&-12 {
			@include suffix('md') {
				margin-bottom: $spacing12;
			}
		}
		&-13 {
			@include suffix('md') {
				margin-bottom: $spacing13;
			}
		}
		&-14 {
			@include suffix('md') {
				margin-bottom: $spacing14;
			}
		}
		&-15 {
			@include suffix('md') {
				margin-bottom: $spacing15;
			}
		}
		&-16 {
			@include suffix('md') {
				margin-bottom: $spacing16;
			}
		}
		&-17 {
			@include suffix('md') {
				margin-bottom: $spacing17;
			}
		}
		&-18 {
			@include suffix('md') {
				margin-bottom: $spacing18;
			}
		}
		&-19 {
			@include suffix('md') {
				margin-bottom: $spacing19;
			}
		}
		&-20 {
			@include suffix('md') {
				margin-bottom: $spacing20;
			}
		}
		&-21 {
			@include suffix('md') {
				margin-bottom: $spacing21;
			}
		}
		&-22 {
			@include suffix('md') {
				margin-bottom: $spacing22;
			}
		}
		&-23 {
			@include suffix('md') {
				margin-bottom: $spacing23;
			}
		}
		&-24 {
			@include suffix('md') {
				margin-bottom: $spacing24;
			}
		}
		&-25 {
			@include suffix('md') {
				margin-bottom: $spacing25;
			}
		}
		&-26 {
			@include suffix('md') {
				margin-bottom: $spacing26;
			}
		}
		&-27 {
			@include suffix('md') {
				margin-bottom: $spacing27;
			}
		}
		&-28 {
			@include suffix('md') {
				margin-bottom: $spacing28;
			}
		}
		&-29 {
			@include suffix('md') {
				margin-bottom: $spacing29;
			}
		}
	}

	@media ($lgUp) {
		&-0 {
			@include suffix('lg') {
				margin-bottom: 0;
			}
		}
		&-1 {
			@include suffix('lg') {
				margin-bottom: $spacing1;
			}
		}
		&-2 {
			@include suffix('lg') {
				margin-bottom: $spacing2;
			}
		}
		&-3 {
			@include suffix('lg') {
				margin-bottom: $spacing3;
			}
		}
		&-4 {
			@include suffix('lg') {
				margin-bottom: $spacing4;
			}
		}
		&-5 {
			@include suffix('lg') {
				margin-bottom: $spacing5;
			}
		}
		&-6 {
			@include suffix('lg') {
				margin-bottom: $spacing6;
			}
		}
		&-7 {
			@include suffix('lg') {
				margin-bottom: $spacing7;
			}
		}
		&-8 {
			@include suffix('lg') {
				margin-bottom: $spacing8;
			}
		}
		&-9 {
			@include suffix('lg') {
				margin-bottom: $spacing9;
			}
		}
		&-10 {
			@include suffix('lg') {
				margin-bottom: $spacing10;
			}
		}
		&-11 {
			@include suffix('lg') {
				margin-bottom: $spacing11;
			}
		}
		&-12 {
			@include suffix('lg') {
				margin-bottom: $spacing12;
			}
		}
		&-13 {
			@include suffix('lg') {
				margin-bottom: $spacing13;
			}
		}
		&-14 {
			@include suffix('lg') {
				margin-bottom: $spacing14;
			}
		}
		&-15 {
			@include suffix('lg') {
				margin-bottom: $spacing15;
			}
		}
		&-16 {
			@include suffix('lg') {
				margin-bottom: $spacing16;
			}
		}
		&-17 {
			@include suffix('lg') {
				margin-bottom: $spacing17;
			}
		}
		&-18 {
			@include suffix('lg') {
				margin-bottom: $spacing18;
			}
		}
		&-19 {
			@include suffix('lg') {
				margin-bottom: $spacing19;
			}
		}
		&-20 {
			@include suffix('lg') {
				margin-bottom: $spacing20;
			}
		}
		&-21 {
			@include suffix('lg') {
				margin-bottom: $spacing21;
			}
		}
		&-22 {
			@include suffix('lg') {
				margin-bottom: $spacing22;
			}
		}
		&-23 {
			@include suffix('lg') {
				margin-bottom: $spacing23;
			}
		}
		&-24 {
			@include suffix('lg') {
				margin-bottom: $spacing24;
			}
		}
		&-25 {
			@include suffix('lg') {
				margin-bottom: $spacing25;
			}
		}
		&-26 {
			@include suffix('lg') {
				margin-bottom: $spacing26;
			}
		}
		&-27 {
			@include suffix('lg') {
				margin-bottom: $spacing27;
			}
		}
		&-28 {
			@include suffix('lg') {
				margin-bottom: $spacing28;
			}
		}
		&-29 {
			@include suffix('lg') {
				margin-bottom: $spacing29;
			}
		}
	}
}

.u-mt {
	&-last-0 {
		> :last-child {
			margin-top: 0;
		}
	}
	&-0 {
		margin-top: 0;
	}
	&-1 {
		margin-top: $spacing1;
	}
	&-2 {
		margin-top: $spacing2;
	}
	&-3 {
		margin-top: $spacing3;
	}
	&-4 {
		margin-top: $spacing4;
	}
	&-5 {
		margin-top: $spacing5;
	}
	&-6 {
		margin-top: $spacing6;
	}
	&-7 {
		margin-top: $spacing7;
	}
	&-8 {
		margin-top: $spacing8;
	}
	&-9 {
		margin-top: $spacing9;
	}
	&-10 {
		margin-top: $spacing10;
	}
	&-11 {
		margin-top: $spacing11;
	}
	&-12 {
		margin-top: $spacing12;
	}
	&-13 {
		margin-top: $spacing13;
	}
	&-14 {
		margin-top: $spacing14;
	}
	&-15 {
		margin-top: $spacing15;
	}
	&-16 {
		margin-top: $spacing16;
	}
	&-17 {
		margin-top: $spacing17;
	}
	&-18 {
		margin-top: $spacing18;
	}
	&-19 {
		margin-top: $spacing19;
	}
	&-20 {
		margin-top: $spacing20;
	}
	&-21 {
		margin-top: $spacing21;
	}
	&-22 {
		margin-top: $spacing22;
	}
	&-23 {
		margin-top: $spacing23;
	}
	&-24 {
		margin-top: $spacing24;
	}
	&-25 {
		margin-top: $spacing25;
	}
	&-26 {
		margin-top: $spacing26;
	}
	&-27 {
		margin-top: $spacing27;
	}
	&-28 {
		margin-top: $spacing28;
	}
	&-29 {
		margin-top: $spacing29;
	}

	@media ($mdUp) {
		&-0 {
			@include suffix('md') {
				margin-top: 0;
			}
		}
		&-1 {
			@include suffix('md') {
				margin-top: $spacing1;
			}
		}
		&-2 {
			@include suffix('md') {
				margin-top: $spacing2;
			}
		}
		&-3 {
			@include suffix('md') {
				margin-top: $spacing3;
			}
		}
		&-4 {
			@include suffix('md') {
				margin-top: $spacing4;
			}
		}
		&-5 {
			@include suffix('md') {
				margin-top: $spacing5;
			}
		}
		&-6 {
			@include suffix('md') {
				margin-top: $spacing6;
			}
		}
		&-7 {
			@include suffix('md') {
				margin-top: $spacing7;
			}
		}
		&-8 {
			@include suffix('md') {
				margin-top: $spacing8;
			}
		}
		&-9 {
			@include suffix('md') {
				margin-top: $spacing9;
			}
		}
		&-10 {
			@include suffix('md') {
				margin-top: $spacing10;
			}
		}
		&-11 {
			@include suffix('md') {
				margin-top: $spacing11;
			}
		}
		&-12 {
			@include suffix('md') {
				margin-top: $spacing12;
			}
		}
		&-13 {
			@include suffix('md') {
				margin-top: $spacing13;
			}
		}
		&-14 {
			@include suffix('md') {
				margin-top: $spacing14;
			}
		}
		&-15 {
			@include suffix('md') {
				margin-top: $spacing15;
			}
		}
		&-16 {
			@include suffix('md') {
				margin-top: $spacing16;
			}
		}
		&-17 {
			@include suffix('md') {
				margin-top: $spacing17;
			}
		}
		&-18 {
			@include suffix('md') {
				margin-top: $spacing18;
			}
		}
		&-19 {
			@include suffix('md') {
				margin-top: $spacing19;
			}
		}
		&-20 {
			@include suffix('md') {
				margin-top: $spacing20;
			}
		}
		&-21 {
			@include suffix('md') {
				margin-top: $spacing21;
			}
		}
		&-22 {
			@include suffix('md') {
				margin-top: $spacing22;
			}
		}
		&-23 {
			@include suffix('md') {
				margin-top: $spacing23;
			}
		}
		&-24 {
			@include suffix('md') {
				margin-top: $spacing24;
			}
		}
		&-25 {
			@include suffix('md') {
				margin-top: $spacing25;
			}
		}
		&-26 {
			@include suffix('md') {
				margin-top: $spacing26;
			}
		}
		&-27 {
			@include suffix('md') {
				margin-top: $spacing27;
			}
		}
		&-28 {
			@include suffix('md') {
				margin-top: $spacing28;
			}
		}
		&-29 {
			@include suffix('md') {
				margin-top: $spacing29;
			}
		}
	}

	@media ($lgUp) {
		&-0 {
			@include suffix('lg') {
				margin-top: 0;
			}
		}
		&-1 {
			@include suffix('lg') {
				margin-top: $spacing1;
			}
		}
		&-2 {
			@include suffix('lg') {
				margin-top: $spacing2;
			}
		}
		&-3 {
			@include suffix('lg') {
				margin-top: $spacing3;
			}
		}
		&-4 {
			@include suffix('lg') {
				margin-top: $spacing4;
			}
		}
		&-5 {
			@include suffix('lg') {
				margin-top: $spacing5;
			}
		}
		&-6 {
			@include suffix('lg') {
				margin-top: $spacing6;
			}
		}
		&-7 {
			@include suffix('lg') {
				margin-top: $spacing7;
			}
		}
		&-8 {
			@include suffix('lg') {
				margin-top: $spacing8;
			}
		}
		&-9 {
			@include suffix('lg') {
				margin-top: $spacing9;
			}
		}
		&-10 {
			@include suffix('lg') {
				margin-top: $spacing10;
			}
		}
		&-11 {
			@include suffix('lg') {
				margin-top: $spacing11;
			}
		}
		&-12 {
			@include suffix('lg') {
				margin-top: $spacing12;
			}
		}
		&-13 {
			@include suffix('lg') {
				margin-top: $spacing13;
			}
		}
		&-14 {
			@include suffix('lg') {
				margin-top: $spacing14;
			}
		}
		&-15 {
			@include suffix('lg') {
				margin-top: $spacing15;
			}
		}
		&-16 {
			@include suffix('lg') {
				margin-top: $spacing16;
			}
		}
		&-17 {
			@include suffix('lg') {
				margin-top: $spacing17;
			}
		}
		&-18 {
			@include suffix('lg') {
				margin-top: $spacing18;
			}
		}
		&-19 {
			@include suffix('lg') {
				margin-top: $spacing19;
			}
		}
		&-20 {
			@include suffix('lg') {
				margin-top: $spacing20;
			}
		}
		&-21 {
			@include suffix('lg') {
				margin-top: $spacing21;
			}
		}
		&-22 {
			@include suffix('lg') {
				margin-top: $spacing22;
			}
		}
		&-23 {
			@include suffix('lg') {
				margin-top: $spacing23;
			}
		}
		&-24 {
			@include suffix('lg') {
				margin-top: $spacing24;
			}
		}
		&-25 {
			@include suffix('lg') {
				margin-top: $spacing25;
			}
		}
		&-26 {
			@include suffix('lg') {
				margin-top: $spacing26;
			}
		}
		&-27 {
			@include suffix('lg') {
				margin-top: $spacing27;
			}
		}
		&-28 {
			@include suffix('lg') {
				margin-top: $spacing28;
			}
		}
		&-29 {
			@include suffix('lg') {
				margin-top: $spacing29;
			}
		}
	}
	@media ($xlUp) {
		&-8 {
			@include suffix('xl') {
				margin-top: $spacing8;
			}
		}
	}
}

.u-pt {
	&-0 {
		padding-top: 0;
	}
	&-1 {
		padding-top: $spacing1;
	}
	&-2 {
		padding-top: $spacing2;
	}
	&-3 {
		padding-top: $spacing3;
	}
	&-4 {
		padding-top: $spacing4;
	}
	&-5 {
		padding-top: $spacing5;
	}
	&-6 {
		padding-top: $spacing6;
	}
	&-7 {
		padding-top: $spacing7;
	}
	&-8 {
		padding-top: $spacing8;
	}
	&-9 {
		padding-top: $spacing9;
	}
	&-10 {
		padding-top: $spacing10;
	}
	&-11 {
		padding-top: $spacing11;
	}
	&-12 {
		padding-top: $spacing12;
	}
	&-13 {
		padding-top: $spacing13;
	}
	&-14 {
		padding-top: $spacing14;
	}
	&-15 {
		padding-top: $spacing15;
	}
	&-16 {
		padding-top: $spacing16;
	}
	&-17 {
		padding-top: $spacing17;
	}
	&-18 {
		padding-top: $spacing18;
	}
	&-19 {
		padding-top: $spacing19;
	}
	&-20 {
		padding-top: $spacing20;
	}
	&-21 {
		padding-top: $spacing21;
	}
	&-22 {
		padding-top: $spacing22;
	}
	&-23 {
		padding-top: $spacing23;
	}
	&-24 {
		padding-top: $spacing24;
	}
	&-25 {
		padding-top: $spacing25;
	}
	&-26 {
		padding-top: $spacing26;
	}
	&-27 {
		padding-top: $spacing27;
	}
	&-28 {
		padding-top: $spacing28;
	}
	&-29 {
		padding-top: $spacing29;
	}

	@media ($mdUp) {
		&-0 {
			@include suffix('md') {
				padding-top: 0;
			}
		}
		&-1 {
			@include suffix('md') {
				padding-top: $spacing1;
			}
		}
		&-2 {
			@include suffix('md') {
				padding-top: $spacing2;
			}
		}
		&-3 {
			@include suffix('md') {
				padding-top: $spacing3;
			}
		}
		&-4 {
			@include suffix('md') {
				padding-top: $spacing4;
			}
		}
		&-5 {
			@include suffix('md') {
				padding-top: $spacing5;
			}
		}
		&-6 {
			@include suffix('md') {
				padding-top: $spacing6;
			}
		}
		&-7 {
			@include suffix('md') {
				padding-top: $spacing7;
			}
		}
		&-8 {
			@include suffix('md') {
				padding-top: $spacing8;
			}
		}
		&-9 {
			@include suffix('md') {
				padding-top: $spacing9;
			}
		}
		&-10 {
			@include suffix('md') {
				padding-top: $spacing10;
			}
		}
		&-11 {
			@include suffix('md') {
				padding-top: $spacing11;
			}
		}
		&-12 {
			@include suffix('md') {
				padding-top: $spacing12;
			}
		}
		&-13 {
			@include suffix('md') {
				padding-top: $spacing13;
			}
		}
		&-14 {
			@include suffix('md') {
				padding-top: $spacing14;
			}
		}
		&-15 {
			@include suffix('md') {
				padding-top: $spacing15;
			}
		}
		&-16 {
			@include suffix('md') {
				padding-top: $spacing16;
			}
		}
		&-17 {
			@include suffix('md') {
				padding-top: $spacing17;
			}
		}
		&-18 {
			@include suffix('md') {
				padding-top: $spacing18;
			}
		}
		&-19 {
			@include suffix('md') {
				padding-top: $spacing19;
			}
		}
		&-20 {
			@include suffix('md') {
				padding-top: $spacing20;
			}
		}
		&-21 {
			@include suffix('md') {
				padding-top: $spacing21;
			}
		}
		&-22 {
			@include suffix('md') {
				padding-top: $spacing22;
			}
		}
		&-23 {
			@include suffix('md') {
				padding-top: $spacing23;
			}
		}
		&-24 {
			@include suffix('md') {
				padding-top: $spacing24;
			}
		}
		&-25 {
			@include suffix('md') {
				padding-top: $spacing25;
			}
		}
		&-26 {
			@include suffix('md') {
				padding-top: $spacing26;
			}
		}
		&-27 {
			@include suffix('md') {
				padding-top: $spacing27;
			}
		}
		&-28 {
			@include suffix('md') {
				padding-top: $spacing28;
			}
		}
		&-29 {
			@include suffix('md') {
				padding-top: $spacing29;
			}
		}
	}

	@media ($lgUp) {
		&-0 {
			@include suffix('lg') {
				padding-top: 0;
			}
		}
		&-1 {
			@include suffix('lg') {
				padding-top: $spacing1;
			}
		}
		&-2 {
			@include suffix('lg') {
				padding-top: $spacing2;
			}
		}
		&-3 {
			@include suffix('lg') {
				padding-top: $spacing3;
			}
		}
		&-4 {
			@include suffix('lg') {
				padding-top: $spacing4;
			}
		}
		&-5 {
			@include suffix('lg') {
				padding-top: $spacing5;
			}
		}
		&-6 {
			@include suffix('lg') {
				padding-top: $spacing6;
			}
		}
		&-7 {
			@include suffix('lg') {
				padding-top: $spacing7;
			}
		}
		&-8 {
			@include suffix('lg') {
				padding-top: $spacing8;
			}
		}
		&-9 {
			@include suffix('lg') {
				padding-top: $spacing9;
			}
		}
		&-10 {
			@include suffix('lg') {
				padding-top: $spacing10;
			}
		}
		&-11 {
			@include suffix('lg') {
				padding-top: $spacing11;
			}
		}
		&-12 {
			@include suffix('lg') {
				padding-top: $spacing12;
			}
		}
		&-13 {
			@include suffix('lg') {
				padding-top: $spacing13;
			}
		}
		&-14 {
			@include suffix('lg') {
				padding-top: $spacing14;
			}
		}
		&-15 {
			@include suffix('lg') {
				padding-top: $spacing15;
			}
		}
		&-16 {
			@include suffix('lg') {
				padding-top: $spacing16;
			}
		}
		&-17 {
			@include suffix('lg') {
				padding-top: $spacing17;
			}
		}
		&-18 {
			@include suffix('lg') {
				padding-top: $spacing18;
			}
		}
		&-19 {
			@include suffix('lg') {
				padding-top: $spacing19;
			}
		}
		&-20 {
			@include suffix('lg') {
				padding-top: $spacing20;
			}
		}
		&-21 {
			@include suffix('lg') {
				padding-top: $spacing21;
			}
		}
		&-22 {
			@include suffix('lg') {
				padding-top: $spacing22;
			}
		}
		&-23 {
			@include suffix('lg') {
				padding-top: $spacing23;
			}
		}
		&-24 {
			@include suffix('lg') {
				padding-top: $spacing24;
			}
		}
		&-25 {
			@include suffix('lg') {
				padding-top: $spacing25;
			}
		}
		&-26 {
			@include suffix('lg') {
				padding-top: $spacing26;
			}
		}
		&-27 {
			@include suffix('lg') {
				padding-top: $spacing27;
			}
		}
		&-28 {
			@include suffix('lg') {
				padding-top: $spacing28;
			}
		}
		&-29 {
			@include suffix('lg') {
				padding-top: $spacing29;
			}
		}
	}
	@media ($xlUp) {
		&-11 {
			@include suffix('xl') {
				padding-top: $spacing11;
			}
		}
		&-16 {
			@include suffix('xl') {
				padding-top: $spacing16;
			}
		}
		&-18 {
			@include suffix('xl') {
				padding-top: $spacing18;
			}
		}
	}
}
