.f-search {
	&__search {
		padding-bottom: $spacing4;
		border-bottom: 1px solid $colorGrayLight;
	}
	&__btn,
	&__close {
		display: flex;
		color: $colorGrayLight;
	}
	&__btn .icon-svg {
		width: 18px;
	}
	&__inp {
		padding: 0;
		border: none;
		color: $colorAbbey;
		font-family: $fontPrimary;
		font-weight: 400;
		font-style: italic;
		font-size: 16px;
		letter-spacing: 0.056em;
		&::placeholder {
			color: $colorGrayDark;
		}
	}
	&__close .icon-svg {
		width: 14px;
	}
	&__loader {
		position: absolute;
		top: calc(50% - 10px);
		left: -42px;
		width: 20px;
		height: 20px;
		border: 2px solid;
		border-radius: 50%;
		border-top-color: transparent;
		background: $colorWhite;
		visibility: hidden;
		opacity: 0;
		transition: opacity $t, visibility $t;
	}

	// STATES
	&__inp.is-loading ~ &__loader {
		visibility: visible;
		opacity: 1;
		animation: animation-rotate 0.8s infinite linear;
	}

	// HOVERS
	.hoverevents &__btn:hover,
	.hoverevents &__close:hover {
		color: $colorGrayDark;
	}

	// MQ
	@media ($mdUp) {
		&__inp {
			font-size: 24px;
		}
		&__loader {
			left: -50px;
		}
	}
	@media ($xlUp) {
		&__search {
			padding: 0;
			border: none;
		}
		&__inp {
			font-size: 30px;
		}

		// MODIF
		&--static &__search {
			padding-bottom: $spacing9;
			border-bottom: 1px solid $colorGrayLight;
		}
	}
}
