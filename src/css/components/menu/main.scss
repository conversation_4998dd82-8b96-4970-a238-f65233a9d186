.m-main {
	line-height: 21px;
	&__list {
		@extend %reset-ul;
		margin: 0 0 -8px;
	}
	&__item {
		@extend %reset-ul-li;
		margin-bottom: 8px;
	}
	&__link {
		position: relative;
		display: block;
		padding: 12px 0;
		color: var(--color-link);
		text-decoration: none;
	}

	// MODIF
	&__item--submenu &__link {
		padding-right: 36px;
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: $spacing4;
			margin-top: -4px;
			border-width: 4px 0 4px 4px;
			border-style: solid dashed;
			border-top-color: transparent;
			border-bottom-color: transparent;
			transition: border-color $t;
		}
	}

	// HOVERS
	&__link.is-active,
	.hoverevents &__link:hover {
		color: $colorMineShaft;
	}
	.light-header &__link.is-active,
	.hoverevents .light-header &__link:hover {
		color: $colorWhite;
	}

	// MQ
	@media ($xlDown) {
		position: relative;
		transition: transform $t;
		&.u-font-label {
			font-size: 18px;
		}
		&__inner {
			position: relative;
			display: block;
		}
		&__btn {
			position: absolute;
			top: 0;
			right: 0;
			width: 36px;
			height: 100%;
		}

		// STATES
		.is-submenu-open & {
			transform: translateX(-100%);
		}
	}
	@media ($smDown) {
		&.u-font-label {
			font-size: 14px;
		}
	}

	@media ($smUp) {
		&__list {
			margin-bottom: -40px;
		}
		&__item {
			margin-bottom: 40px;
		}
		&__link {
			padding: 11px 0;
		}
	}
	@media ($xlUp) {
		&__list {
			display: flex;
			justify-content: center;
			align-items: center;
			margin: 0 -14px;
		}
		&__item {
			margin: 0;
		}
		&__link {
			margin-bottom: -1px;
			padding: 51px 10px 52px;
		}
		&__btn {
			display: none;
		}

		// MODIF
		&__item--submenu &__link {
			&::before {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				margin-left: -10px;
				border-width: 0 10px 10px;
				border-style: solid dashed;
				border-color: $colorWhite transparent;
				visibility: hidden;
				opacity: 0;
				transition: opacity $t, visibility 0s $t;
			}
			&::after {
				border-width: 4px 4px 0;
				border-color: var(--color-link) transparent;
			}
		}

		// HOVERS
		.hoverevents &__item--submenu:hover &__link {
			z-index: 60;
			color: $colorWhite;
			&::before {
				visibility: visible;
				opacity: 1;
				transition-delay: 0s, 0s;
			}
			&::after {
				border-color: $colorWhite transparent;
			}
		}
	}
}
