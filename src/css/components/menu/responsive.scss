.m-responsive {
	$s: &;
	position: relative;
	border: 48px solid transparent;
	border-width: 0 0 0 48px;
	// display: none;

	&__title,
	&__link {
		position: relative;
		display: inline-block;
		padding: 16px 0;
		border-bottom: 1px solid transparent;
		color: var(--color-link);
		font-family: $fontSecondary;
		font-size: 13px;
		line-height: (16/13);
		text-transform: uppercase;
		text-decoration: none;
		transition: color $t, border-color $t;
	}
	&__title {
		padding-right: 36px;
		&::after {
			content: '';
			position: absolute;
			top: 21px;
			right: $spacing4;
			border-width: 4px 4px 0;
			border-style: solid dashed;
			border-right-color: transparent;
			border-left-color: transparent;
			transition: transform $t;
		}
	}
	&__popup {
		position: absolute;
		top: 100%;
		right: 0;
		width: 200px;
		background-color: $colorWhite;
		visibility: hidden;
		opacity: 0;
		transition: opacity $t, visibility $t;
		box-shadow: 0 0 24px 0 rgba($colorMineShaft, 0.08);
	}
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		padding: 6px 15px;
	}
	&__link {
		padding: 0;
	}

	// STATEs & HOVERs
	&__item.is-active &__link {
		color: $colorText;
	}
	&.is-open &__title,
	.hoverevents &__title:hover,
	.hoverevents &__link:hover {
		border-bottom-color: $colorMineShaft;
		color: $colorMineShaft;
	}
	&.is-open {
		#{$s}__title::after {
			transform: rotate(-180deg);
		}
		#{$s}__popup {
			visibility: visible;
			opacity: 1;
		}
	}
}
