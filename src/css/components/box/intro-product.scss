.b-intro-product {
	padding: 81px 0 $spacing8;
	background: var(--color-bg);
	overflow: hidden;
	&:first-child {
		margin-top: -$hHeightSm;
	}
	&__media {
		margin: 0 (-$rowMainGutterSm);
		.img {
			background: $colorWhite;
			&::before {
				padding-top: percentage(304/320);
			}
		}
	}
	&__info {
		color: $colorRaven;
		a {
			text-decoration: none;
		}
	}
	&__groups {
		display: flex;
		flex-wrap: wrap;
		margin: 0 0 (-$spacing4) (-$spacing4);
	}
	&__group {
		flex: 0 0 50%;
		border: $spacing8 solid transparent;
		border-width: 0 0 $spacing4 $spacing4;
		&--download,
		&--style,
		&--btns {
			flex: 1 1 100%;
		}
	}
	&__favourite {
		line-height: 1px;
	}
	&__list {
		@extend %reset-ul;
		color: $colorLink;
	}
	&__item {
		@extend %reset-ul-li;
	}

	// VARIANTs
	&__group--btns {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		margin-left: -$spacing6;
		> * {
			margin-left: $spacing6;
		}
	}

	.b-intro-event &__groups {
		margin: 0 0 (-$spacing6) (-$spacing10);
		padding: 0;
	}
	.b-intro-event &__group {
		border-width: 0 0 $spacing6 $spacing10;
	}
	.b-intro-event &__list,
	.b-intro-event &__link {
		color: $colorGrayDark;
	}

	// HOVERS
	.hoverevents .light-header &__link:hover {
		color: $colorWhite;
	}

	// MQ
	@media ($mdDown) {
		&__title {
			font-size: 32px;
			line-height: (38/32);
		}
		&__group--style &__list {
			display: flex;
			margin: 0 0 0 (-$spacing4);
		}
		&__group--style &__item {
			flex: 0 0 50%;
			border: $spacing4 solid transparent;
			border-width: 0 0 0 $spacing4;
		}
	}
	@media ($lgDown) {
		&__group--btns {
			justify-content: space-between;
		}
		&__groups-wrap {
			border-top: 1px solid $colorBd;
		}
	}

	@media ($mdUp) {
		padding: 100px 0 $spacing9;
		&:first-child {
			margin-top: -$hHeightMd;
		}
		&__groups {
			justify-content: space-between;
			align-items: flex-start;
		}
		&__group {
			flex: 0 0 auto;

			&--btns {
				flex: 1 1 auto;
			}
		}
		&__media {
			margin: 0 (-$rowMainGutter);
			.img::before {
				padding-top: percentage(512/768);
			}
		}
		&__variants {
			max-width: 184px;
		}

		// MODIF
		.b-intro-event &__groups {
			justify-content: flex-start;
		}
		.b-intro-event &__group {
			flex: 0 0 auto;
		}
	}
	@media ($lgUp) {
		padding: 154px 0 $spacing17;
		&:first-child {
			margin-top: -$hHeightLg;
		}
		&__media {
			position: relative;
			margin: 0;
			padding: 0 $spacing8;
			&::before {
				content: '';
				position: absolute;
				top: $spacing8;
				right: 0;
				bottom: $spacing8;
				left: 0;
				border: 1px solid $colorBd;
			}
			.img::before {
				padding-top: percentage(624/768);
			}
		}
		&__variants {
			max-width: 272px;
		}

		&__groups {
			margin: 0 0 (-$spacing8) (-$spacing8);
		}
		&__group {
			flex: 1 1 auto;
			border-width: 0 0 $spacing8 $spacing8;
		}
	}
}
