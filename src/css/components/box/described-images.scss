.b-described-images {
	$s: &;
	max-width: 100%;
	&__img {
		position: relative;
		display: block;
		width: 100%;
		height: 100%;
		border-bottom: 0;
		overflow: hidden;
		&::before {
			padding-top: percentage(224/296);
		}
	}
	img {
		transition: transform 0.8s;
	}
	&__text {
		display: block;
		font-size: 13px;
		line-height: (23/13);
	}

	// VARIANTS
	&--2-1 {
		#{$s}__list {
			padding-top: 24px;
		}
		#{$s}__item {
			&:nth-child(1) {
				#{$s}__img::before {
					padding-top: percentage(224/307);
				}
			}
			&:nth-child(2) {
				#{$s}__img::before {
					padding-top: percentage(360/296);
				}
			}
			&:nth-child(3) {
				display: flex;
				align-items: flex-end;
			}
		}
	}
	&--2-2 &__list {
		padding-top: 32px;
	}
	&--3-1 {
		#{$s}__list {
			padding-top: 24px;
		}
		#{$s}__item {
			&:nth-child(1) {
				margin-top: 25px;
				#{$s}__img::before {
					padding-top: percentage(224/296);
				}
			}
			&:nth-child(2) {
				#{$s}__img::before {
					padding-top: percentage(360/296);
				}
			}
			&:nth-child(3) {
				margin-bottom: 25px;
				#{$s}__img::before {
					padding-top: percentage(224/296);
				}
			}
			&:nth-child(4) {
				margin-top: 40px;
			}
		}
	}
	&--3-3 {
		#{$s}__item {
			&:nth-child(1) {
				#{$s}__img::before {
					padding-top: percentage(360/296);
				}
			}
		}
	}

	// MQ
	@media ($mdDown) {
		&__item {
			margin-bottom: 16px;
		}

		// VARIANTS
		&--2-3 {
			#{$s}__item {
				&:nth-child(2) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 480px;
						background: var(--color-bg);
					}
				}
			}
		}
		&--3-3 {
			#{$s}__item {
				&:nth-child(1) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 480px;
						background: var(--color-bg);
					}
				}
				&:nth-child(5) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 184px;
						background: var(--color-bg);
					}
				}
			}
		}
		&--4-3 {
			#{$s}__item {
				&:nth-child(1) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 480px;
						background: var(--color-bg);
					}
				}
				&:nth-child(5),
				&:nth-child(7) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 184px;
						background: var(--color-bg);
					}
				}
			}
		}
		&--5-3 {
			#{$s}__item {
				&:nth-child(1) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 480px;
						background: var(--color-bg);
					}
				}
				&:nth-child(5),
				&:nth-child(6),
				&:nth-child(8) {
					position: relative;
					padding-top: 24px;
					&::before {
						content: '';
						position: absolute;
						top: 0;
						left: -12px;
						z-index: -1;
						width: 280px;
						height: 184px;
						background: var(--color-bg);
					}
				}
			}
		}
	}
	@media ($lgDown) {
		.content-indented {
			padding: 0;
		}
	}

	@media ($mdUp), print {
		&__list {
			position: relative;
			display: grid;
			grid-template-columns: repeat(6, 1fr);
			grid-template-rows: repeat(16, 1fr);
			gap: 24px;
			&::before {
				content: '';
				position: absolute;
				z-index: -1;
			}
		}
		// VARIANTS
		&--2-1 {
			#{$s}__list {
				grid-template-rows: repeat(4, 1fr);
				padding-top: 40px;
				&::before {
					top: 0;
					right: 40px;
					left: 40px;
					width: auto;
					height: 377px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 2 / 1 / 4 / 4;
					margin-top: -65px;
					#{$s}__img::before {
						padding-top: 100%;
					}
				}
				&:nth-child(2) {
					grid-area: 4 / 1 / 5 / 4;
				}
				&:nth-child(3) {
					grid-area: 1 / 4 / 5 / 7;
					#{$s}__img::before {
						padding-top: percentage(472/336);
					}
				}
			}
		}
		&--3-1 {
			#{$s}__list {
				grid-template-rows: repeat(4, 1fr);
				&::before {
					top: 56px;
					right: 40px;
					bottom: auto;
					left: 40px;
					width: auto;
					height: 464px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 1 / 3 / 4;
					#{$s}__img::before {
						padding-top: percentage(392/352);
					}
				}
				&:nth-child(2) {
					grid-area: 2 / 4 / 4 / 7;
					margin-top: -49px;
					margin-bottom: -49px;
					#{$s}__img::before {
						padding-top: percentage(512/424);
					}
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 5 / 4;
					#{$s}__img::before {
						padding-top: percentage(392/352);
					}
				}
				&:nth-child(4) {
					grid-area: 4 / 4 / 5 / 7;
				}
			}
		}
		&--2-2 {
			#{$s}__list {
				grid-template-rows: repeat(5, 1fr);
				padding-top: 64px;
				&::before {
					top: 0;
					right: -36px;
					width: 612px;
					height: 467px;
					background: var(--color-bg);
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 4 / 3 / 7;
					margin-top: 24px;
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 5 / 4;
					margin-bottom: -20px;
					#{$s}__img::before {
						padding-top: percentage(736/688);
					}
				}
				&:nth-child(3) {
					grid-area: 3 / 4 / 6 / 7;
					#{$s}__img::before {
						padding-top: 100%;
					}
				}
				&:nth-child(4) {
					grid-area: 5 / 1 / 7 / 4;
					margin-top: 20px;
				}
			}
		}
		&--2-3 {
			#{$s}__list {
				grid-template-rows: repeat(7, 1fr);
				padding-top: 88px;
				&::before {
					top: 0;
					right: 40px;
					width: 692px;
					height: 544px;
					background: var(--color-bg);
				}
				&::after {
					content: '';
					position: absolute;
					top: 480px;
					left: -216px;
					z-index: -1;
					width: 496px;
					height: 440px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 4 / 4 / 7;
					#{$s}__img::before {
						padding-top: percentage(732/688);
					}
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 4;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 7 / 4;
					#{$s}__img::before {
						padding-top: percentage(711/448);
					}
				}
				&:nth-child(4) {
					grid-area: 4 / 4 / 6 / 7;
				}
				&:nth-child(5) {
					grid-area: 7 / 1 / 9 / 4;
				}
			}
		}
		&--3-3 {
			#{$s}__list {
				grid-template-rows: repeat(7, 1fr);
				padding-top: 88px;
				&::before {
					top: 0;
					right: 40px;
					width: 692px;
					height: 544px;
					background: var(--color-bg);
				}
				&::after {
					content: '';
					position: absolute;
					bottom: 240px;
					left: -216px;
					z-index: -1;
					width: 360px;
					height: 296px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 4 / 4 / 7;
					#{$s}__img::before {
						padding-top: percentage(732/688);
					}
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 4;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 7 / 4;
					#{$s}__img::before {
						padding-top: percentage(712/448);
					}
				}
				&:nth-child(4) {
					grid-area: 7 / 4 / 8 / 7;
				}
				&:nth-child(5) {
					grid-area: 4 / 4 / 7 / 7;
					#{$s}__img::before {
						padding-top: percentage(572/448);
					}
				}
				&:nth-child(6) {
					grid-area: 7 / 1 / 8 / 4;
				}
			}
		}
		&--4-3 {
			#{$s}__list {
				grid-template-rows: repeat(11, 1fr);
				padding-top: 88px;
				&::before {
					top: 0;
					right: 40px;
					width: 692px;
					height: 544px;
					background: var(--color-bg);
				}
				&::after {
					content: '';
					position: absolute;
					bottom: 266px;
					left: -216px;
					z-index: -1;
					width: 360px;
					height: 296px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 4 / 5 / 7;
					#{$s}__img::before {
						padding-top: percentage(732/688);
					}
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 4;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 4;
					#{$s}__img::before {
						padding-top: percentage(711/448);
					}
				}
				&:nth-child(4) {
					grid-area: 10 / 1 / 12 / 4;
				}
				&:nth-child(5) {
					grid-area: 8 / 4 / 11 / 7;
					#{$s}__img::before {
						padding-top: percentage(500/688);
					}
				}
				&:nth-child(6) {
					grid-area: 11 / 4 / 12 / 7;
				}
				&:nth-child(7) {
					grid-area: 5 / 4 / 8 / 7;
					#{$s}__img::before {
						padding-top: percentage(376/448);
					}
				}
			}
		}
		&--5-3 {
			#{$s}__list {
				grid-template-rows: repeat(14, 1fr);
				padding-top: 88px;
				&::before {
					top: 0;
					right: 40px;
					width: 692px;
					height: 544px;
					background: var(--color-bg);
				}
				&::after {
					content: '';
					position: absolute;
					bottom: 160px;
					left: -216px;
					z-index: -1;
					width: 360px;
					height: 296px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 4 / 5 / 7;
					#{$s}__img::before {
						padding-top: percentage(732/688);
					}
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 4;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 4;
					#{$s}__img::before {
						padding-top: percentage(711/448);
					}
				}
				&:nth-child(4) {
					grid-area: 11 / 4 / 13 / 7;
				}
				&:nth-child(5) {
					grid-area: 5 / 4 / 8 / 7;
					#{$s}__img::before {
						padding-top: percentage(376/448);
					}
				}
				&:nth-child(6) {
					grid-area: 8 / 4 / 11 / 7;
					#{$s}__img::before {
						padding-top: percentage(500/688);
					}
				}
				&:nth-child(7) {
					grid-area: 13 / 1 / 15 / 4;
				}
				&:nth-child(8) {
					grid-area: 10 / 1 / 13 / 4;
					#{$s}__img::before {
						padding-top: percentage(460/568);
					}
				}
			}
		}
	}
	@media ($lgUp) {
		&__list {
			grid-template-columns: repeat(12, 1fr);
			gap: 32px;
		}
		&__text {
			font-size: 14px;
			line-height: (24/14);
		}
		// VARIANTS
		.grid__cell > &--2-1 {
			margin-right: -$rowMainGutter;
		}
		&--2-1 {
			#{$s}__list {
				grid-template-rows: 1fr repeat(3, 1fr) 1fr;
				&::before {
					right: auto;
					left: 120px;
					width: 840px;
					height: 594px;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 2 / 1 / 5 / 7;
					#{$s}__img::before {
						padding-top: percentage(506/448);
					}
				}
				&:nth-child(2) {
					grid-area: 5 / 2 / 6 / 7;
				}
				&:nth-child(3) {
					grid-area: 1 / 7 / 6 / 13;
					#{$s}__img::before {
						padding-top: percentage(728/464);
					}
				}
			}
		}
		&--2-2 {
			#{$s}__list {
				grid-template-rows: repeat(6, 1fr);
				padding-top: 96px;
				&::before {
					right: -120px;
					width: 840px;
					height: 704px;
				}
				&::after {
					content: '';
					position: absolute;
					right: 32px;
					bottom: 80px;
					z-index: -2;
					width: 600px;
					height: 456px;
					border: 1px solid $colorBd;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 2 / 5 / 8;
				}
				&:nth-child(2) {
					grid-area: 1 / 8 / 3 / 11;
					margin-top: 40px;
				}
				&:nth-child(3) {
					grid-area: 3 / 8 / 7 / 12;
					#{$s}__img::before {
						padding-top: percentage(712/448);
					}
				}
				&:nth-child(4) {
					grid-area: 5 / 2 / 6 / 6;
				}
			}
		}
		&--3-1 {
			#{$s}__list {
				grid-template-columns: repeat(11, 1fr);
				&::before {
					top: 80px;
					right: 85px;
					bottom: auto;
					left: auto;
					width: 1003px;
					height: 465px;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 1 / 3 / 6;
				}
				&:nth-child(2) {
					grid-area: 2 / 6 / 4 / 12;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 5 / 6;
				}
				&:nth-child(4) {
					grid-area: 4 / 6 / 5 / 10;
				}
			}
		}
		&--2-3 {
			#{$s}__list {
				grid-template-rows: repeat(10, 1fr);
				&::before {
					left: -376px;
					width: 1096px;
					height: 704px;
				}
				&::after {
					right: -120px;
					left: auto;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 7 / 13;
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 6;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 6;
				}
				&:nth-child(4) {
					grid-area: 7 / 6 / 10 / 10;
				}
				&:nth-child(5) {
					grid-area: 10 / 1 / 11 / 6;
				}
			}
		}
		&--3-3 {
			#{$s}__list {
				grid-template-rows: repeat(10, 1fr);
				&::before {
					left: -376px;
					width: 1096px;
					height: 704px;
				}
				&::after {
					right: -120px;
					left: auto;
					width: 888px;
					height: 456px;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 7 / 13;
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 6;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 6;
				}
				&:nth-child(4) {
					grid-area: 7 / 10 / 10 / 13;
				}
				&:nth-child(5) {
					grid-area: 7 / 6 / 11 / 10;
				}
				&:nth-child(6) {
					grid-area: 10 / 1 / 12 / 6;
				}
			}
		}
		&--4-3 {
			#{$s}__list {
				grid-template-rows: repeat(15, 1fr);
				&::before {
					left: -376px;
					width: 1096px;
					height: 704px;
				}
				&::after {
					right: -120px;
					left: auto;
					width: 888px;
					height: 400px;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 7 / 13;
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 6;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 6;
				}
				&:nth-child(4) {
					grid-area: 10 / 1 / 12 / 6;
				}
				&:nth-child(5) {
					grid-area: 11 / 6 / 16 / 13;
					margin-top: -30px;
					margin-bottom: 42px;
				}
				&:nth-child(6) {
					grid-area: 7 / 10 / 10 / 13;
				}
				&:nth-child(7) {
					grid-area: 7 / 6 / 11 / 10;
					margin-bottom: 28px;
				}
			}
		}
		&--5-3 {
			#{$s}__list {
				grid-template-rows: repeat(16, 1fr);
				&::before {
					left: -376px;
					width: 1096px;
					height: 704px;
				}
				&::after {
					right: -120px;
					left: auto;
					width: 888px;
					height: 456px;
				}
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 7 / 13;
				}
				&:nth-child(2) {
					grid-area: 1 / 1 / 3 / 6;
				}
				&:nth-child(3) {
					grid-area: 3 / 1 / 10 / 6;
					margin-bottom: 12px;
				}
				&:nth-child(4) {
					grid-area: 7 / 10 / 10 / 13;
				}
				&:nth-child(5) {
					grid-area: 7 / 6 / 11 / 10;
					margin-bottom: 28px;
				}
				&:nth-child(6) {
					grid-area: 11 / 6 / 16 / 13;
					margin-top: -30px;
					margin-bottom: 42px;
				}
				&:nth-child(7) {
					grid-area: 15 / 1 / 16 / 6;
					margin-top: -64px;
				}
				&:nth-child(8) {
					grid-area: 10 / 1 / 15 / 6;
					margin-top: -10px;
					margin-bottom: 64px;
				}
			}
		}
	}
	@media ($xlUp) {
		// VARIANTS
		.grid__cell > &--2-1 {
			margin-right: calc((100vw - #{$rowMainWidth}) / -2);
		}
		&--2-3 {
			#{$s}__list {
				grid-template-rows: repeat(9, 1fr);
			}
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 7 / 12;
				}
				&:nth-child(2) {
					grid-area: 1 / 3 / 3 / 6;
				}
				&:nth-child(3) {
					grid-area: 3 / 2 / 9 / 6;
					margin-bottom: 32px;
				}
				&:nth-child(4) {
					grid-area: 7 / 6 / 9 / 9;
				}
				&:nth-child(5) {
					grid-area: 9 / 2 / 10 / 5;
					margin-top: -32px;
				}
			}
		}
		&--3-3 {
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 8 / 12;
				}
				&:nth-child(2) {
					grid-area: 1 / 4 / 4 / 6;
				}
				&:nth-child(3) {
					grid-area: 4 / 2 / 11 / 6;
				}
				&:nth-child(4) {
					grid-area: 8 / 10 / 12 / 12;
				}
				&:nth-child(5) {
					grid-area: 8 / 6 / 12 / 10;
				}
				&:nth-child(6) {
					grid-area: 11 / 2 / 12 / 5;
				}
			}
		}
		&--4-3 {
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 8 / 12;
				}
				&:nth-child(2) {
					grid-area: 1 / 4 / 4 / 6;
				}
				&:nth-child(3) {
					grid-area: 4 / 2 / 11 / 6;
				}
				&:nth-child(4) {
					grid-area: 11 / 2 / 17 / 5;
				}
				&:nth-child(5) {
					grid-area: 12 / 6 / 17 / 12;
				}
				&:nth-child(6) {
					grid-area: 8 / 10 / 12 / 12;
				}
				&:nth-child(7) {
					grid-area: 8 / 6 / 12 / 10;
				}
			}
		}
		&--5-3 {
			#{$s}__item {
				&:nth-child(1) {
					grid-area: 1 / 6 / 8 / 12;
				}
				&:nth-child(2) {
					grid-area: 1 / 3 / 4 / 6;
				}
				&:nth-child(3) {
					grid-area: 4 / 2 / 11 / 6;
				}
				&:nth-child(4) {
					grid-area: 8 / 10 / 12 / 12;
				}
				&:nth-child(5) {
					grid-area: 8 / 6 / 12 / 10;
				}
				&:nth-child(6) {
					grid-area: 12 / 6 / 17 / 12;
				}
				&:nth-child(7) {
					grid-area: 16 / 1 / 18 / 5;
				}
				&:nth-child(8) {
					grid-area: 11 / 1 / 16 / 6;
				}
			}
		}
	}
}
