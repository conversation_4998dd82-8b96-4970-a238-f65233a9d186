.b-description-list {
	text-align: center;
	&__wrap {
		position: relative;
	}
	&__item {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: -50px;
			left: 50%;
			width: 24px;
			height: 1px;
			margin-left: -12px;
			background: rgba($colorBd, 0.5);
		}
	}
	&__title {
		position: relative;
		padding-bottom: $spacing5;
		&::before {
			content: '';
			position: absolute;
			bottom: 0;
			left: 50%;
			width: 40px;
			height: 1px;
			margin-left: -20px;
			background: $colorRaven;
		}
	}

	// MQ
	@media ($smDown) {
		&__item {
			padding: 0 $spacing2;
		}
	}

	@media ($lgDown) {
		font-size: 16px;
		line-height: (25 / 16);
	}

	@media ($mdUp) {
		&__item {
			padding: 0 $spacing5;
			&::before {
				top: 125px;
				right: $spacing4 * -1;
				left: auto;
				width: 1px;
				height: 40px;
			}
			&:last-child {
				&::before {
					display: none;
				}
			}
		}
	}

	@media ($lgUp) {
		&__item {
			&::before {
				top: 115px;
				height: 80px;
			}
		}
		&__title {
			padding-bottom: $spacing9;
			&::before {
				width: 54px;
				margin-left: -27px;
			}
		}
	}
}
