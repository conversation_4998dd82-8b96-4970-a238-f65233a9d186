.b-share {
	padding-top: $spacing6;
	padding-bottom: $spacing6;
	background: var(--color-bg);
	text-align: center;
	&__title {
		margin-bottom: $spacing5;
	}
	&__list {
		display: flex;
		gap: $spacing9;
		justify-content: center;
		margin: 0;
	}
	&__item {
		color: $colorText;
		text-decoration: none;
		.icon-svg {
			width: 20px;
		}
	}

	// MODIF
	&--mid-gold {
		background: $colorGoldMid;
	}

	// HOVERS
	.hoverevents &__item:hover {
		color: $colorRaven;
	}
	.hoverevents &--mid-gold &__item:hover {
		color: $colorWhite;
	}

	// MQ
	@media ($mdUp) {
		padding-top: $spacing8;
		padding-bottom: $spacing8;
		&__list {
			gap: $spacing11;
		}
	}

	@media ($lgUp) {
		text-align: left;
		&__inner {
			display: flex;
			gap: $spacing9;
			justify-content: space-between;
			align-items: center;
		}
		&__title {
			margin-bottom: 0;
		}
	}
}
