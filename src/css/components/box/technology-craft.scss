.b-technology-craft {
	&__label {
		display: block;
		margin-bottom: 24px;
	}
	&__img {
		width: 248px;
		height: 318px;
	}

	// MQ
	@media ($mdUp) {
		&__text {
			columns: 2;
			column-gap: 32px;
		}
		&__img {
			width: 540px;
			height: 360px;
		}
	}
	@media ($lgUp) {
		&__header {
			position: relative;
			padding-top: $spacing10;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				width: $rowMainWidth;
				height: 664px;
				border: 1px solid $colorGrayLight;
				transform: translateX(-50%);
			}
		}
		.is-initialized &__img {
			width: 696px;
			height: 464px;
			// width: auto;
			// &::before {
			// 	content: none;
			// }
			// img {
			// 	position: static;
			// 	width: auto;
			// 	height: 100%;
			// }
		}
	}
}
