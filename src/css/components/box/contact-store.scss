.b-contact-store {
	$s: &;
	background: var(--color-bg);
	font-size: 14px;
	line-height: (24 / 14);
	&__wrapper {
		display: flex;
		flex-direction: column-reverse;
	}
	&__content {
		padding: $spacing10 0 $spacing11;
	}
	&__img::before {
		padding-top: percentage(304/320);
	}
	&__hours.u-font-label {
		font-family: $fontPrimary;
	}

	//MQ
	@media ($mdDown) {
		&__img {
			margin: 0 -12px;
		}
	}

	@media ($mdUp) {
		&__wrapper {
			flex-direction: row;
		}
		&__content {
			flex: 0 0 auto;
			align-self: center;
			width: 50%;
			padding: $spacing10 $spacing8 $spacing11 $spacing9;
		}
		&__img {
			flex: 0 0 auto;
			width: 50vw;
			&::before {
				padding-top: percentage(552/384);
			}
		}

		//VARIANTs
		&__wrapper--reversed {
			flex-direction: row-reverse;
		}
		&__wrapper--reversed &__inner {
			margin-left: auto;
		}
		&__wrapper--reversed &__content {
			padding: $spacing10 0 $spacing11 $spacing12;
		}
	}

	@media ($lgUp) {
		&__img::before {
			padding-top: percentage(816/960);
		}
		&__content {
			padding: $spacing19 $spacing12 $spacing19 0;
		}
		&__wrapper--reversed &__content {
			padding: $spacing19 0 $spacing19 $spacing12;
		}
	}
}
