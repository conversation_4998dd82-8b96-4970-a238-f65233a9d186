.b-event {
	$s: &;
	font-size: 14px;
	line-height: 24px;
	&__img::before {
		padding-top: percentage(320/448);
	}
	&__categories {
		position: absolute;
		bottom: $spacing4;
		left: $spacing4;
		left: 0;
		display: flex;
		align-items: flex-start;
		margin: 0;
		color: $colorWhite;
		font-family: $fontSecondary;
		font-weight: 600;
		font-size: 10px;
		line-height: 16px;
		text-transform: uppercase;
	}
	&__category {
		padding-left: $spacing4;
	}
	&__content {
		padding: $spacing4 $spacing6 0;
	}
	&__title {
		display: flex;
		gap: 10px 20px;
		flex-wrap: wrap;
		justify-content: space-between;
		margin: 0 0 $spacing4;
	}
	&__info {
		margin-bottom: $spacing4;
	}

	//VARIANTs
	&--lg {
		#{$s}__img::before {
			padding-top: percentage(360/664);
		}
		#{$s}__content {
			padding: $spacing6 $spacing6 0;
		}
		#{$s}__title {
			margin: 0 0 $spacing6;
		}
		#{$s}__info {
			margin-bottom: $spacing6;
		}
	}
}
