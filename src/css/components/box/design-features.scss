.b-design-features {
	&__img-wrap {
		position: relative;
		max-width: none;
		&::before {
			content: '';
			position: absolute;
			top: $spacing7;
			bottom: $spacing7;
			left: -1000px;
			width: 2000px;
			border: 1px solid $colorBd;
		}
	}
	&__img::before {
		padding-top: percentage(224/296);
	}

	//MQ
	@media ($smUp) {
		&__img::before {
			padding-top: percentage(416/696);
		}
	}

	@media ($mdUp) {
		&__img-wrap::before {
			top: $spacing9;
			bottom: $spacing9;
		}
	}

	@media ($lgUp) {
		&__img-wrap {
			&::before {
				content: '';
				position: absolute;
				top: 64px;
				right: 0;
				bottom: 58px;
				left: auto;
				width: 2000px;
				border: 1px solid $colorBd;
			}
		}
	}
	@media ($xlUp) {
		&__img-wrap {
			padding-right: $spacing15;
		}
		&__img {
			height: 100%;
			min-height: 644px;
			overflow: visible;
			img {
				right: 0;
				left: auto;
				width: auto;
				max-width: none;
			}
		}
	}
}
