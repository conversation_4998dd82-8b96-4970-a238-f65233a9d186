.b-download {
	font-size: 14px;
	line-height: 24px;
	&__img {
		&::before {
			padding-top: percentage(192/208);
		}
		&::after {
			content: '';
			position: absolute;
			top: 45%;
			right: 0;
			bottom: 0;
			left: 0;
			background: linear-gradient(180deg, rgba($colorBlack, 0.0001) 0%, rgba($colorBlack, 0.4) 100%);
		}
	}
	&__categories {
		position: absolute;
		bottom: 10px;
		left: 0;
		z-index: 1;
		display: flex;
		color: $colorWhite;
		font-family: $fontSecondary;
		font-weight: 600;
		font-size: 10px;
		line-height: 12px;
		text-transform: uppercase;
		pointer-events: none;
	}
	&__category {
		padding-left: $spacing4;
	}
	&__title {
		margin: $spacing6 0 $spacing4;
		letter-spacing: 0.03em;
	}
	&__btn .btn__text {
		font-size: 12px;
	}

	// MQ

	@media ($mdUp) {
		&__categories {
			bottom: $spacing4;
		}
	}
}
