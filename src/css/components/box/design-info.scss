.b-design-info {
	$s: '.b-design-info';
	position: relative;
	padding-top: 48px;
	font-size: 13px;
	line-height: 24px;
	letter-spacing: normal;
	overflow: hidden;
	&::before {
		content: '';
		position: absolute;
		top: 0;
		right: -2000px;
		left: -2000px;
		height: 480px;
		background: var(--color-bg);
	}
	&__title {
		position: relative;
		letter-spacing: 1px;
	}
	&__desc {
		position: relative;
		margin-bottom: $spacing6;
	}
	&__img {
		margin-bottom: $spacing9;
		&::before {
			padding-top: percentage(224/296);
		}
	}

	&__item {
		position: relative;
		margin-bottom: $spacing9;
		padding-left: $spacing12;
		&::before {
			content: '';
			position: absolute;
			top: -9px;
			left: 0;
			width: 46px;
			height: 46px;
			border: 1px solid rgba($colorGoldMid, 0.35);
			border-radius: 40px;
			background: rgba($colorGoldMid, 0.35);
			outline: 1px solid rgba($colorGoldMid, 0.35);
			opacity: 0.5;
		}
		&::after {
			content: '0' counter(item);
			counter-increment: step;
			position: absolute;
			top: 0;
			left: 0;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 28px;
			height: 28px;
			margin-left: 9px;
			border-radius: 20px;
			background: rgba($colorGoldMid, 0.35);
			font-weight: 600;
			font-size: 13px;
			line-height: (25 / 13);
			letter-spacing: 0.03em;
		}
	}
	&__subtitle {
		letter-spacing: 1px;
	}

	//VARIANTs
	&--bg-down {
		padding-top: 0;
		padding-bottom: $spacing10;
		&::before {
			display: none;
		}
		#{$s}__wrapper {
			padding-top: $spacing10;
		}

		#{$s}__img {
			z-index: 1;
		}
		#{$s}__list {
			position: relative;
			&::before {
				content: '';
				position: absolute;
				top: -100px;
				right: -2000px;
				bottom: $spacing10 * -1;
				left: -2000px;
				background: var(--color-bg);
			}
		}
		#{$s}__item {
			&::before {
				border: 2px solid rgba($colorWhite, 0.35);
				background: rgba($colorWhite, 0.75);
				outline: none;
			}
			&::after {
				background: $colorWhite;
			}
		}
	}

	// MQ
	@media ($smDown) {
		// VARIANTs
		&--bg-down {
			#{$s}__img {
				position: relative;
				overflow: visible;
				&::before {
					content: '';
					position: absolute;
					top: -20px;
					bottom: 60px;
					left: 50%;
					width: 2000px;
					border: 1px solid $colorBd;
				}
				img {
					position: relative;
				}
			}
		}
	}

	@media ($smUp) {
		&__list {
			display: flex;
			flex-wrap: wrap;
			margin: $spacing5 0 $spacing8 * -1 $spacing6 * -1;
		}
		&__item {
			flex: 0 1 50%;
			margin-bottom: 0;
			padding: 65px 0 $spacing8 $spacing6;
			&::before {
				top: 0;
				left: $spacing6;
			}
			&::after {
				top: 9px;
				left: $spacing6;
			}
		}
	}

	@media ($mdUp) {
		&::before {
			height: 575px;
		}
		&__desc {
			column-gap: $spacing8;
			column-count: 2;
			margin-bottom: $spacing10;
		}
		&__img {
			margin-bottom: $spacing10;
			&::before {
				padding-top: percentage(384/616);
			}
		}
		&__item {
			flex: 0 1 33.333%;
			&::after {
				top: 9px;
			}
		}

		//VARIANTs
		&__item:nth-child(3n + 2) {
			margin-top: $spacing6;
		}
		&__item:nth-child(3n + 3) {
			margin-top: $spacing10;
		}
		&--bg-down {
			#{$s}__wrapper {
				border: 1px solid $colorBd;
			}
			#{$s}__list {
				&::before {
					top: -160px;
				}
			}
		}
	}

	@media ($lgUp) {
		font-size: 14px;
		&::before {
			right: $gridGutter;
		}
		&__wrapper {
			display: grid;
			grid-template-columns: percentage(448/1168) 1fr;
			grid-template-areas:
				'title desc'
				'img img'
				'list list';
			gap: $spacing13 $spacing8;
			padding-top: $spacing15;
		}
		&__title {
			grid-area: title;
		}
		&__desc {
			grid-area: desc;
			margin-bottom: 0;
			padding-top: $spacing10;
		}
		&__img {
			grid-area: img;
			margin-bottom: 0;
			&::before {
				padding-top: percentage(688/1168);
			}
		}
		&__list {
			grid-area: list;
			margin: $spacing5 0 $spacing8 * -1 $spacing8 * -1;
		}
		&__item {
			padding: 0 0 $spacing8 $spacing8;
			&::before {
				top: -9px;
				left: $spacing8;
			}
			&::after {
				top: 0;
				left: $spacing8;
			}
		}
		&__item-inner {
			padding-left: 70px;
		}

		//VARIANTs
		&__item:nth-child(3n + 2) {
			margin-top: $spacing9;
		}
		&__item:nth-child(3n + 3) {
			margin-top: $spacing14;
		}
		&--bg-down {
			#{$s}__wrapper {
				padding-top: $spacing15;
			}
			#{$s}__list {
				&::before {
					top: -245px;
				}
			}
		}
	}

	@media ($xlUp) {
		&::before {
			height: 888px;
		}
		&__list {
			margin: $spacing5 0 $spacing8 * -1 $spacing16 * -1;
		}
		&__item {
			padding: 0 0 $spacing8 $spacing16;
			&::before {
				left: $spacing16;
			}
			&::after {
				left: $spacing16;
			}
		}
	}
}
