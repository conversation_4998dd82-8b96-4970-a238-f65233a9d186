.b-contact-details {
	$s: &;
	font-size: 14px;
	line-height: (20 / 14);
	letter-spacing: 0.03em;
	text-align: left;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__wrap {
		display: flex;
		align-items: center;
		margin: 0 0 $spacing4 * -1 $spacing4 * -1;
		& > span {
			margin: 0 0 $spacing4 $spacing4;
			white-space: nowrap;
		}
	}
	&__icon {
		position: relative;
		flex: 0 0 auto;
		padding-right: $spacing4;
		border-right: 1px solid $colorBd;
		color: $colorRaven;
		.icon-svg {
			width: 20px;
			height: 20px;
		}
		.icon-svg--phone {
			width: 20px;
			height: 15px;
		}
	}
	&__info {
		text-align: center;
	}
	&__note {
		display: block;
		margin-left: 54px;
	}
	&__contact {
		display: block;
		gap: 0.5rem;
		max-width: calc(100% - 32px - 36px);
		padding-bottom: 1px;
		text-overflow: ellipsis;
		overflow: hidden;
	}

	//VARIANTs
	&--light {
		color: $colorWhite;
		#{$s}__link {
			&::before,
			&::after {
				background: $colorRaven;
			}
		}
		#{$s}__icon {
			border-right-color: $colorAbbey;
		}
	}

	//STATEs of VARIANTs
	.hoverevents &--light &__link:hover {
		color: $colorWhite;
		&::after {
			background: $colorWhite;
		}
	}
}
