.b-intro-reference {
	padding: 81px 0 $spacing8;
	overflow: hidden;
	&:first-child {
		margin-top: -$hHeightSm;
	}
	&__media {
		margin: 0 (-$rowMainGutterSm);
		.img {
			background: $colorWhite;
			&::before {
				padding-top: percentage(304/320);
			}
		}
	}
	&__content {
		display: flex;
		flex-direction: column;
	}
	&__info {
		color: var(--color-link);
	}
	&__logo {
		margin-top: auto;
	}
	&__groups {
		display: flex;
		flex-wrap: wrap;
		align-items: flex-start;
		margin: 0 0 (-$spacing4) (-$spacing3);

		& & {
			margin: 0 0 0 auto;
			padding: 0;
		}
	}
	&__group {
		flex: 0 1 auto;
		padding: 0 0 $spacing4 $spacing3;
	}
	&__favourite {
		color: var(--color-link);
		line-height: 1px;
	}
	&__list {
		@extend %reset-ul;
		color: var(--color-link);
	}
	&__item {
		@extend %reset-ul-li;
	}
	&__link {
		color: var(--color-link);
	}

	// VARIANTs
	&__group--btns {
		display: flex;
		justify-content: flex-end;
		align-items: center;
		align-self: center;
		margin-left: -$spacing6;
		> * {
			margin-left: $spacing6;
		}
	}
	&__list--inline {
		display: flex;
		flex-wrap: wrap;
		margin-left: -$spacing6;
	}
	&__list--inline &__item {
		padding-left: $spacing6;
	}

	// MQ
	@media ($mdDown) {
		&__title-main {
			font-size: 32px;
			line-height: (38/32);
		}
	}
	@media ($lgDown) {
		&__group--btns {
			flex: 1 1 auto;
			min-width: 100%;
		}
		&__group .btn {
			margin-left: auto;
		}
	}

	@media ($mdUp) {
		padding: 100px 0 $spacing8;
		&:first-child {
			margin-top: -$hHeightMd;
		}
		&__media {
			margin: 0 (-$rowMainGutter);
			.img::before {
				padding-top: percentage(507/768);
			}
		}
		&__groups {
			margin: 0 0 (-$spacing8) (-$spacing9);
		}
		&__group {
			padding: 0 0 $spacing8 $spacing9;
		}

		@media ($lgDown) {
			&__groups &__groups {
				flex: 0 0 100%;
			}
		}
	}
	@media ($lgUp) {
		padding: 154px 0 $spacing9;
		&:first-child {
			margin-top: -$hHeightLg;
		}
		&__groups {
			flex-wrap: nowrap;
			& & {
				flex-wrap: wrap;
				justify-content: flex-end;
			}
		}
		&__group {
			flex: 0 0 auto;
		}
	}
	@media ($xlUp) {
		&__title-main {
			padding-top: $spacing11;
		}
		&__groups {
			margin: 0 0 0 (-$spacing12);
			padding-top: $spacing15;
		}
		&__group {
			padding: 0 0 0 $spacing12;
		}
		&__media {
			position: relative;
			margin: 0;
			padding: 0 $spacing8;
			&::before {
				content: '';
				position: absolute;
				top: $spacing8;
				right: 0;
				bottom: $spacing8;
				left: 0;
				border: 1px solid $colorBd;
			}
			.img {
				width: 1096px;
				&::before {
					padding-top: percentage(606/1096);
				}
			}
		}
	}
}
