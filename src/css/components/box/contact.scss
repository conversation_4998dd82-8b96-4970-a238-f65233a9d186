.b-contact {
	$s: &;
	color: $colorWhite;
	font-size: 14px;
	line-height: (26 / 14);
	text-align: center;
	&__wrapper {
		padding: $spacing12 $spacing13 $spacing13;
		background: $colorBlack;
	}
	&__subtitle {
		margin: $spacing2 0 0;
		color: $colorRaven;
	}
	&__branch {
		margin: $spacing6 0 0;
		font-size: 16px;
		line-height: (26 / 16);
	}
	&__address {
		max-width: 160px;
		margin: $spacing2 auto 0;
	}
	&__img-wrapper {
		padding-top: 40px;
	}
	&__img {
		max-width: 120px;
		margin: $spacing8 auto $spacing4;
	}
	&__contact-details {
		padding-top: $spacing9;
	}
	&__name {
		font-size: 16px;
	}

	//VARIANTs
	&--default {
		#{$s} {
			&__helper {
				margin-top: $spacing9;
				padding-top: $spacing8;
				border-top: 1px solid $colorAbbey;
			}
		}
	}
	&--pulled {
		margin-right: -$rowMainGutterSm;
		margin-left: -$row<PERSON>ain<PERSON>utterSm;
		padding-top: $spacing11;
		text-align: left;
		#{$s}__wrapper {
			padding: 0 $rowMainGutterSm $spacing12;
			background: $colorBlack;
		}
		#{$s}__img {
			max-width: 112px;
			margin: 0 0 $spacing9;
			&:first-child {
				margin-top: -$spacing11;
			}
		}
		#{$s}__contact-details {
			margin-top: $spacing6;
			padding-top: $spacing7;
			border-top: 1px solid $colorAbbey;
		}
	}
	&--footer {
		padding-top: 0;
		text-align: left;
		#{$s}__wrapper {
			padding: 0 1px;
			background: none;
		}
		#{$s}__top {
			display: grid;
			grid-template-columns: 1fr auto;
			grid-template-rows: 1fr auto;
			grid-template-areas:
				'title img'
				'person person';
			gap: $spacing9 $spacing3;
		}
		#{$s}__footer-title {
			grid-area: title;
			max-width: 140px;
			margin-top: $spacing5;
			font-size: 24px;
			line-height: (34/24);
		}
		#{$s}__img {
			grid-area: img;
			width: 120px;
			max-width: none;
			margin: 0;
		}
		#{$s}__contact-details {
			margin-top: $spacing8;
			padding-top: $spacing7;
			border-top: 1px solid $colorAbbey;
		}
		#{$s}__person {
			grid-area: person;
		}
	}
	&--office {
		#{$s}__wrapper {
			padding: $spacing11 $rowMainGutterSm;
		}
		#{$s}__title {
			font-size: 24px;
			line-height: (34/24);
		}
		#{$s}__subtitle {
			color: $colorGrayDark;
		}
		#{$s}__helper {
			margin-top: $spacing9;
			padding-top: $spacing9;
			border-top: 1px solid $colorMineShaft;
		}
		#{$s}__contact-details {
			max-width: 300px;
			margin: 0 auto;
		}
		#{$s}__name {
			font-size: 14px;
		}
	}
	&--intro {
		text-align: left;
		#{$s}__wrapper {
			position: relative;
			padding: $spacing9 0 $spacing9 $spacing13;
			&::before {
				content: '';
				position: absolute;
				top: 0;
				bottom: 0;
				left: 100%;
				display: block;
				width: 2000px;
				background: $colorBlack;
			}
		}
		#{$s}__helper {
			display: grid;
			grid-template-areas:
				'img contact'
				'person contact';
			align-items: end;
			column-gap: 20px;
		}
		#{$s}__img {
			grid-area: img;
			max-width: 68px;
			margin: 0 0 $spacing6;
			border-radius: 50%;
			overflow: hidden;
		}
		#{$s}__person {
			grid-area: person;
		}
		#{$s}__contact-details {
			grid-area: contact;
			padding: 0 0 5px;
		}
	}

	// MQ
	@media ($smUp) {
		&--footer {
			#{$s}__top {
				grid-template-areas:
					'title img'
					'person img';
			}
			#{$s}__img {
				width: 216px;
			}
		}
	}
	@media ($mdUp) {
		&__branch {
			margin: $spacing8 0 0;
			font-size: 20px;
			line-height: (32 / 20);
		}

		// MODIF
		&--footer {
			#{$s}__contact-details {
				margin-top: $spacing6;
			}
			#{$s}__footer-title {
				max-width: 340px;
				margin-top: 0;
				font-size: 32px;
				line-height: (38/32);
			}
		}
		&--pulled {
			margin-right: 0;
			margin-left: 0;
			padding-top: $spacing10;
			#{$s}__wrapper {
				padding: $spacing11 $spacing9;
			}
			#{$s}__img {
				max-width: 208px;
				&:first-child {
					margin-top: -$spacing17;
				}
			}

			@media ($lgDown) {
				#{$s}__helper {
					display: grid;
					grid-template-columns: 208px 1fr;
					grid-template-areas:
						'img person'
						'img contact';
					gap: 0 70px;
				}
				#{$s}__img {
					grid-area: img;
				}
				#{$s}__person {
					grid-area: person;
				}
				#{$s}__contact-details {
					grid-area: contact;
				}
			}
		}
		&--office {
			#{$s}__wrapper {
				padding: $spacing11;
			}
			#{$s}__title {
				font-size: 32px;
				line-height: (38/32);
			}
		}

		@media ($lgDown) {
			&--office {
				#{$s}__helper {
					display: grid;
					grid-template-columns: 208px 1fr;
					grid-template-rows: auto 1fr;
					grid-template-areas: 'img person' 'img contact';
					grid-gap: 0 $spacing12;
				}
				#{$s}__img {
					grid-area: img;
					width: 100%;
					max-width: none;
				}
				#{$s}__person {
					grid-area: person;
					font-size: 13px;
					line-height: (23/13);
					text-align: left;
				}
				#{$s}__contact-details {
					grid-area: contact;
					margin-top: $spacing6;
					border-top: 1px solid $colorMineShaft;
				}
				#{$s}__branch {
					font-size: 24px;
					line-height: (34/24);
				}
			}
		}
	}
	@media ($lgUp) {
		&__name {
			font-size: 14px;
		}

		// MODIF
		&--footer {
			#{$s}__contact-details {
				margin-top: $spacing4;
			}
			#{$s}__top {
				grid-template-areas:
					'title img'
					'person person';
			}
			#{$s}__img {
				width: 88px;
			}
			#{$s}__footer-title {
				max-width: 120px;
				font-size: 20px;
				line-height: (32/20);
			}
		}
		&--office {
			#{$s}__wrapper {
				padding: $spacing12 $spacing14;
			}
			#{$s}__title {
				font-size: 30px;
				line-height: (48/30);
			}
		}
	}
	@media ($xlUp) {
		&--pulled {
			padding-top: $spacing17;
			#{$s}__wrapper {
				padding: 1px $spacing12 $spacing15 $spacing14;
			}
			#{$s}__img {
				max-width: 212px;
			}
		}
	}
}
