.b-scope {
	padding: $spacing14 0;
	font-size: 14px;
	line-height: (24 / 14);
	letter-spacing: normal;
	.c-std {
		&__list {
			grid-template-columns: percentage(448/1168) 1fr 1fr;
			gap: $spacing8;
		}
		&__item:first-child {
			.b-std__img::before {
				padding-top: percentage(592/448);
			}
		}
	}
	.b-std {
		&__img::before {
			padding-top: percentage(256/328);
		}
		&--lg &__img::before {
			padding-top: percentage(592/448);
		}
	}
	&__wrapper {
		display: grid;
		grid-template-columns: percentage(448/1168) 1fr;
		grid-template-areas:
			'title desc'
			'list list';
		gap: $spacing13 $spacing8;
	}
	&__desc {
		column-gap: $spacing8;
		column-count: 2;
	}
	&__title {
		grid-area: title;
		letter-spacing: 1px;
	}
	&__list {
		grid-area: list;
	}
}
