.b-project-highlight {
	$s: &;
	font-size: 13px;
	line-height: 24px;
	&__img {
		position: relative;
		max-width: 128px;
		margin: 0 auto $spacing6;
	}

	// MQ
	@media ($mdDown) {
		text-align: center;
		&__title::before {
			left: 50%;
			margin-left: -20px;
		}
		&__content {
			padding: 0 $spacing8;
		}
	}

	@media ($mdUp) {
		&__inner {
			position: relative;
			display: flex;
			gap: $spacing13;
			align-items: flex-start;
			max-width: 580px;
		}
		&__img {
			position: relative;
			flex: 0 0 auto;
			width: percentage(216 / 580);
			max-width: none;
			margin: 0;
		}
		&__content {
			flex: 1 1 auto;
			align-self: center;
		}

		// MODIF
		&__item:nth-child(2n) {
			#{$s}__inner {
				flex-direction: row-reverse;
				margin-left: auto;
			}
		}
		&__item:nth-child(4n + 2),
		&__item:nth-child(4n + 3) {
			#{$s}__img::before {
				content: '';
				position: absolute;
				top: $spacing6 * -1;
				bottom: $spacing6 * -1;
				width: 1000px;
				border: 1px solid $colorBd;
				pointer-events: none;
			}
		}
		&__item:nth-child(4n + 2) {
			#{$s}__img::before {
				left: 50%;
			}
		}
		&__item:nth-child(4n + 3) {
			#{$s}__img::before {
				right: 50%;
			}
		}
	}

	@media ($lgUp) {
		font-size: 13px;
		&__list {
			padding: $spacing11 0;
		}
		&__inner {
			gap: $spacing10;
			max-width: 570px;
		}
		&__img {
			width: percentage(248 / 570);
		}
		//VARIANTs
		&__item:nth-child(2n) {
			#{$s}__inner {
				flex-direction: row;
				margin: 0;
			}
		}
		&__item:nth-child(4n + 2),
		&__item:nth-child(4n + 3) {
			#{$s}__img::before {
				display: none;
			}
		}
		&__item:nth-child(4n + 3),
		&__item:nth-child(4n + 4) {
			#{$s}__inner {
				flex-direction: row-reverse;
				margin-left: auto;
			}
		}
		&__item:nth-child(4n + 1),
		&__item:nth-child(4n + 4) {
			#{$s}__img {
				position: relative;
				&::before {
					content: '';
					position: absolute;
					top: -$spacing11;
					bottom: -$spacing11;
					width: 1000px;
					border: 1px solid $colorBd;
					pointer-events: none;
				}
			}
		}
		&__item:nth-child(4n + 1) {
			#{$s}__img::before {
				right: 65%;
			}
		}
		&__item:nth-child(4n + 4) {
			#{$s}__img {
				position: relative;
				&::before {
					left: 65%;
				}
			}
		}
	}
}
