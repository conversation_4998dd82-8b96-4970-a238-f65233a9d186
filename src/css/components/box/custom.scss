.b-custom {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	align-items: center;
	padding-bottom: 36px;
	&__buttons {
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		z-index: 100;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	&__fullscreen,
	&__close {
		@include button-reset;
		position: relative;
		width: 72px;
		height: 72px;
		color: $colorWhite;
		outline: none;
		.icon-svg {
			position: absolute;
			top: 50%;
			left: 50%;
			width: 14px;
			transform: translate(-50%, -50%);
		}
	}
	&__image {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		picture {
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
		}
		img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}
	&__poster {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}
	&__video {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		video {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}
	&__youtube {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		&::after {
			content: '';
			position: absolute;
			top: 0;
			right: 0;
			bottom: 0;
			left: 0;
		}
		iframe {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover;
			object-position: center;
		}
	}
	&__title {
		position: relative;
		z-index: 200;
		color: $colorWhite;
	}
	&__description {
		position: relative;
		z-index: 200;
		color: $colorWhite;
	}

	// Variants
	.b-modal--fullscreen &__title h2 {
		font-size: 30px;
	}
	.b-modal--fullscreen &__title {
		display: none;
	}
	.b-modal--fullscreen &__description {
		display: none;
	}
	.b-modal--fullscreen &__button {
		display: none;
	}

	// Media
	@media ($mdDown) {

	}

	@media ($lgUp) {

	}
}
