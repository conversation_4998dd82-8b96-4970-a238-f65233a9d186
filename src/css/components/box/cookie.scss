@import '@superkoders/cookie/src/css/components/box/cookie';

.b-cookie {
	font-size: 13px;
	&__bg {
		background: rgba($colorBlack, 0.5);
	}
	&__box {
		padding: $rowMainGutterSm;
		background: $colorWhite;
	}
	&__option {
		& + & {
			border-top: 1px solid $colorWhite;
		}
	}
	&__option-head {
		> span {
			&::before {
				top: 8px;
			}
			strong {
				cursor: pointer;
			}
		}
	}

	&__btns {
		.btn__text {
			min-width: 0;
		}
	}
	@media ($mdUp) {
		align-items: flex-end;
		font-size: 16px;
		&__box {
			max-width: 890px;
			margin-bottom: 1rem;
			padding: 25px;
		}
	}
}
