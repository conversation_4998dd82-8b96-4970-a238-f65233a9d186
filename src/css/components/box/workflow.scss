.b-workflow {
	$s: &;
	position: relative;
	padding: $spacing10 $spacing5 $spacing9;
	border: 1px solid $colorBd;
	&__img {
		position: absolute;
		z-index: 1;
		width: 80px;
		height: 180px;
		object-fit: contain;
		&--left {
			bottom: 20%;
			left: 0;
			object-position: left center;
		}
		&--right {
			top: 50%;
			right: 0;
			object-position: right center;
		}
	}
	&__title {
		font-size: 30px;
		line-height: (36/30);
	}
	&__list {
		counter-reset: item;
		position: relative;
		margin: 0;
		padding: 0;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			bottom: 0;
			left: 50%;
			width: 1px;
			background: $colorBd;
		}
	}
	&__item {
		position: relative;
		margin: 70px 0 0;
		padding: 0 0 13px;
		background: $colorWhite;
		&::before {
			content: '';
			counter-increment: none;
			position: absolute;
			top: 0;
			left: 50%;
			width: 60px;
			height: 60px;
			margin-left: -30px;
			border-radius: 50%;
			// background: #eff0f1;
			background: var(--color-bg);
			box-shadow: 0 0 0 2px var(--color-light), 0 0 0 2px #eff0f1, 0 0 0 20px $colorWhite;
			// box-shadow: 0 0 0 2px #e4e5e9, 0 0 0 2px #eff0f1, 0 0 0 20px $colorWhite;
		}
		&::after {
			content: counter(item);
			counter-increment: item;
			position: absolute;
			top: 11px;
			left: 50%;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 38px;
			height: 38px;
			margin-left: -19px;
			border-radius: 50%;
			background: var(--color-light);
		}
	}
	&__content {
		display: block;
		padding-top: 75px;
		text-align: center;
	}

	// MODIF
	&__item:nth-child(-n + 9)::after {
		content: '0' counter(item);
	}

	// MQ
	@media ($mdDown) {
		&__item {
			font-size: 16px;
			line-height: (26 / 16);
		}
	}

	@media ($mdUp) {
		padding: $spacing12 $spacing5 $spacing14;
		&__img {
			width: 190px;
			height: 355px;
		}
		&__title {
			font-size: 32px;
			line-height: (38/32);
		}
		&__item {
			margin-top: 77px;
			padding: 0;
		}
		&__content {
			display: flex;
			align-items: center;
			width: 50%;
			min-height: 60px;
			margin-left: auto;
			padding: 0 0 0 65px;
		}
		// 	VARIANTs
		&__item:nth-child(2n) {
			#{$s}__content {
				justify-content: flex-end;
				margin-left: 0;
				padding: 0 65px 0 0;
				text-align: right;
			}
		}
	}

	@media ($lgUp) {
		padding: $spacing15 $spacing5 $spacing19;
		&__img {
			top: 50%;
			bottom: auto;
			width: 30%;
			height: 100%;
			transform: translateY(-50%);
			&--left {
				left: 50%;
				margin-left: -50vw;
			}
			&--right {
				right: 50%;
				margin-right: -50vw;
			}
		}
		&__title {
			font-size: 40px;
			line-height: (60/40);
		}
		&__item {
			margin-top: 156px;
			&::before {
				width: 90px;
				height: 90px;
				margin-left: -45px;
			}
			&::after {
				top: 17px;
				width: 56px;
				height: 56px;
				margin-left: -28px;
			}
		}
		&__content {
			min-height: 90px;
		}
	}
	@media ($xlUp) {
		&__img {
			width: 480px;
			height: 1000px;
		}
	}
}
