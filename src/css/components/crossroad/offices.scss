.c-offices {
	font-size: 14px;
	line-height: (24 / 14);
	text-align: center;
	&__wrap {
		position: relative;
		overflow: hidden;
	}
	&__item {
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 0 30px;
	}
	&__subtitle.u-font-label {
		color: $colorRaven;
	}
	&__btn {
		position: relative;
		margin-top: auto;
	}

	// MQ
	@media ($lgDown) {
		&__subtitle.u-font-label {
			font-size: 10px;
			line-height: (16 / 10);
		}
		&__btn {
			.btn__text {
				font-size: 10px;
			}
		}
	}

	@media ($mdUp) {
		&__item {
			&::after {
				content: '';
				position: absolute;
				top: 0;
				right: -16px;
				width: 1px;
				height: 190px;
				background: $colorBd;
			}
		}
		// VARIANTs
		&__item:last-child {
			&::after {
				display: none;
			}
		}
	}

	@media ($lgUp) {
		&__item {
			&::after {
				top: 50%;
				height: 136px;
				transform: translateY(-50%);
			}
		}
	}
}
