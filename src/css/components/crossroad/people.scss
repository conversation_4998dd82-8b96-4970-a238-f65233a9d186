.c-people {
	.embla {
		overflow: visible;
	}
	&__wrapper {
		position: relative;
		margin: 0 -1px;
		padding: 24px 0;
		border: 1px solid $colorGrayLight;
	}
	&__item {
		width: 256px;
	}
	&__btns {
		padding-top: $spacing8;
	}

	// MQ
	// @media ($xlDown) {
	// 	// STATES
	// 	.js & .b-people__content {
	// 		visibility: hidden;
	// 		opacity: 0;
	// 		transition: opacity $t, visibility $t;
	// 	}
	// 	.js &__item.is-selected .b-people__content {
	// 		visibility: visible;
	// 		opacity: 1;
	// 	}
	// }
	@media ($mdDown) {
		&__list {
			margin-left: -8px;
		}
		&__item {
			border-left-width: 8px;
		}
		.content-indented {
			padding: 0 24px;
		}
	}
	@media ($mdUp) {
		&__wrapper {
			padding: 56px 0;
		}
		&__item {
			width: 400px;
		}
		&__btns {
			padding-top: $spacing9;
		}
	}
	@media ($lgUp) {
		&__wrapper {
			padding: 96px 0;
		}
		&__btns {
			padding-top: $spacing10;
		}
	}
	// @media (min-width: 1640px) {
	// 	// STATES
	// 	.js & .b-people__content {
	// 		visibility: hidden;
	// 		opacity: 0;
	// 		transition: opacity $t, visibility $t;
	// 	}
	// 	.js &__item.is-selected .b-people__content {
	// 		visibility: visible;
	// 		opacity: 1;
	// 	}
	// }
}
