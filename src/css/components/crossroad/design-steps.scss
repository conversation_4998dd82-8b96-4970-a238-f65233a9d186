.c-design-steps {
	font-size: 14px;
	line-height: (24 /14);
	letter-spacing: normal;
	&__list {
		@extend %reset-ol;
		counter-reset: step;
	}
	&__item {
		@extend %reset-ol-li;
	}
	.b-composition-tabs {
		margin-top: 48px;
	}

	// VARIANTs
	&__item--w-carousel {
		position: relative;
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 50%;
			width: 100%;
			max-width: 1168px;
			height: 912px;
			border: 1px solid $colorBd;
			transform: translateX(-50%);
			pointer-events: none;
		}
		.b-design-step__wrapper {
			&::before {
				display: none;
			}
		}
	}

	// MQ
	@media ($mdUp) {
		.b-composition-tabs {
			margin-top: 88px;
		}
		// VARIANTs
		&__item--w-carousel {
			padding: 64px 32px 0;
			&::before {
				height: 665px;
			}
		}
	}
	@media ($lgUp) {
		.b-composition-tabs {
			margin-top: 112px;
		}
		// VARIANTs
		&__item--w-carousel {
			padding-top: 88px;
			&::before {
				height: 976px;
			}
		}
	}
}
