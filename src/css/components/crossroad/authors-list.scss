.c-authors-list {
	background: $colorBg;
	font-size: 14px;
	line-height: (24/14);
	.b-author-sm {
		&__img {
			width: 56px;
			height: 56px;
			background: $colorWhite;
		}
	}
	&__title {
		margin: 0 0 $spacing9;
	}
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		margin-top: $spacing9;
	}

	&__desc {
		margin: $spacing6 0 0;
	}

	// MQ
	@media ($mdDown) {
		padding: $spacing9 $spacing8;
		&__title {
			font-size: 24px;
			line-height: (34/24);
		}
	}
	@media ($mdUp) {
		display: flex;
		padding-top: $spacing9;
		padding-bottom: $spacing9;
		&__title {
			flex: 0 0 155px;
		}
		&__list {
			flex: 1 1 auto;
			padding: $spacing6 0 0 $gridGutter;
		}
		&__desc {
			margin: $spacing8 0 0;
		}
	}
	@media ($lgUp) {
		padding-top: $spacing12;
		padding-bottom: $spacing16;
		&__title {
			flex: 0 0 208px;
		}
		&__list {
			padding: $spacing8 0 0 $gridGutter;
		}
		&__item {
			display: flex;
		}
		&__author {
			flex: 0 0 208px;
		}
		&__desc {
			margin: 0;
			padding-left: $gridGutter;
		}
	}
}
