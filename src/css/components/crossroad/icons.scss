.c-icons {
	font-size: 13px;
	line-height: (23 / 13);
	text-align: center;
	&__wrap {
		position: relative;
		overflow: hidden;
	}
	&__list {
		margin: 0 0 $spacing14 * -1;
	}
	&__item {
		padding: 0 $spacing7;
		border-width: 0 0 $spacing14;
		&::after {
			content: '';
			position: absolute;
			bottom: -40px;
			left: 50%;
			width: 32px;
			height: 1px;
			background: $colorBd;
			transform: translateX(-50%);
		}
	}
	&__icon {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 72px;
		img {
			width: auto;
			max-height: 100%;
		}
	}
	&__desc {
		position: relative;
	}

	// VARIANTs
	&__icon--noimg {
		width: 72px;
		margin: 0 auto;
		border-radius: 50%;
		background: var(--color-bg);
	}

	// STATEs
	&__item:last-child {
		&::after {
			display: none;
		}
	}

	// MQ
	@media ($smUp) {
		&__item {
			padding: 0 $spacing11;
		}
	}

	@media ($mdUp) {
		&__item {
			padding: 0 $spacing7;
			border-width: 0 0 $spacing14;
			&::after {
				top: 147px;
				right: -2px;
				left: auto;
				width: 1px;
				height: 32px;
			}
		}
	}

	@media ($lgUp) {
		font-size: 14px;
		line-height: (24 / 14);
		&__item {
			padding: 0 $spacing9;
			&::after {
				top: 167px;
				height: 48px;
			}
		}
	}
}
