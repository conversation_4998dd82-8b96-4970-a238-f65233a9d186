import { initModal } from '@superkoders/modal';
import { createModal } from '@superkoders/modal';
import { Controller } from 'stimulus';
import { iconSvg } from '../tools/iconSvg';
import naja from 'naja';
import { MQ } from '../tools/MQ';

const fetchLoader = async (url, element) =>
	await fetch(url, {
		headers: {
			'X-Requested-With': 'XMLHttpRequest',
		},
	}).then((response) => {
		const contentType = response.headers.get('Content-Type');

		if (contentType.includes('application/json')) {
			return response.json().then((data) => {
				if (element && element.dataset.snippetid && data.snippets && data.snippets[element.dataset.snippetid]) {
					return `<div id="${element.dataset.snippetid}">${data.snippets[element.dataset.snippetid]}</div>`;
				} else {
					return 'Error: No valid snippet id provided.';
				}
			});
		} else {
			return response.text();
		}
	});

const buildStructure = (modal, { header, titleElem, descElem, content, prevElem, nextElem, navElem, loader, bg }) => {
	const wrapper = document.createElement('div');
	wrapper.classList.add('b-modal__wrapper');

	wrapper.appendChild(header);
	// wrapper.appendChild(titleElem);
	// wrapper.appendChild(descElem);
	wrapper.appendChild(content);
	wrapper.appendChild(prevElem);
	wrapper.appendChild(nextElem);
	// wrapper.appendChild(navElem);
	modal.appendChild(bg);
	modal.appendChild(wrapper);
	modal.appendChild(loader);
};

const buildStructureCustom = (modal, { content, loader, bg }) => {
	const wrapper = document.createElement('div');
	wrapper.classList.add('b-modal__wrapper');

	wrapper.appendChild(content);
	modal.appendChild(bg);
	modal.appendChild(wrapper);
	modal.appendChild(loader);
};

export const defaultOptions = {
	closeOnBgClick: true,
	removeOnClose: true,
	closeIcon: iconSvg('close-thick'),
	prevIcon: iconSvg('arrow-left'),
	nextIcon: iconSvg('arrow-right'),
	loaderTpl: function() {
		return '<div class="body-loader__loader"></div>';
	},
	onModalContentLoaded: function() {
		naja.uiHandler.bindUI(this);
	},
	fetchLoader,
	buildStructure,
};

export const customOptions = {
	closeOnBgClick: true,
	removeOnClose: true,
	closeIcon: iconSvg('close-thick'),
	loaderTpl: function() {
		return '<div class="body-loader__loader"></div>';
	},
	onModalContentLoaded: function() {
		naja.uiHandler.bindUI(this);
	},
	fetchLoader,
	buildStructure: buildStructureCustom,
};
export default class ModalController extends Controller {
	initialize() {
		initModal(defaultOptions);
	}
	connect() {
		document.querySelectorAll('[data-dynamic-modal]').forEach((el) => {
			const modalId = el.id;
			const modalColor = el.dataset.dynamicModalColor;
			const spacingMobile = el.dataset.dynamicModalSpacingMobile;
			const spacingTablet = el.dataset.dynamicModalSpacingTablet;
			const spacingDesktop = el.dataset.dynamicModalSpacingDesktop;

			let custom;

			const options = {
				...customOptions,
				customWrapperClass: `b-modal--custom b-modal--${modalColor}`,
				onModalContentLoaded() {
					naja.uiHandler.bindUI(this);

					requestAnimationFrame(() => {
						const modalRoot = this.closest('.b-modal--custom');

						if (modalRoot) {
							modalRoot.style.padding = spacingMobile + 'px';
							if (MQ('mdUp')) modalRoot.style.padding = spacingTablet + 'px';
							if (MQ('lgUp')) modalRoot.style.padding = spacingDesktop + 'px';
						}

						this.querySelectorAll('.js-modal-close').forEach((btn) => {
							btn.addEventListener('click', (event) => {
								event.preventDefault();

								const href = btn.getAttribute('href');
								const modalNumericId = parseInt(el.id.replace(/^.*?-/, ''), 10);

								fetch(`?do=modal-closeModal&modal-id=${modalNumericId}`, {
									headers: { 'X-Requested-With': 'XMLHttpRequest' },
								})
									.catch((err) => console.error('Chyba při ukládání do session', err))
									.finally(() => {
										custom.methods.close();

										if (href && href !== '#') {
											setTimeout(() => {
												window.location.href = href;
											}, 100);
										}
									});
							});
						});

						this.querySelectorAll('.js-modal-fullscreen').forEach((btn) => {
							btn.addEventListener('click', (event) => {
								event.preventDefault();
								const modalRoot = event.currentTarget.closest('.b-modal');
								modalRoot.classList.toggle('b-modal--fullscreen');
							});
						});
					});
				}
			};

			custom = createModal(
				[
					{
						url: `#${modalId}`,
					},
				],
				options,
			);

			custom.methods.open();
		});
	}
}
