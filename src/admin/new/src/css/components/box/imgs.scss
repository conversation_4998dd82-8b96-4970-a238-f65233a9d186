.b-imgs {
	&__list {
		min-height: $xs;
	}
	&__inner {
		position: relative;
		padding-top: 100%;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background: $colorWhite;
		background-image: linear-gradient(45deg, #ccc 25%, transparent 25%), linear-gradient(-45deg, #ccc 25%, transparent 25%),
		linear-gradient(45deg, transparent 75%, #ccc 75%), linear-gradient(-45deg, transparent 75%, #ccc 75%);
		background-size: 20px 20px;
		background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
		transition: border-color $t;
		box-shadow: $shadow;
		.btn-icon {
			background: rgba($colorWhite, 0.35);
		}
	}
	&__img {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		display: flex;
		justify-content: center;
		align-items: center;
		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
			&[src*='noimg.svg'] {
				max-width: 60%;
				max-height: 60%;
			}
		}
	}
	&__dragdrop {
		position: absolute;
		top: $xs;
		left: $xs;
	}
	&__btns {
		position: absolute;
		top: $xs;
		right: $xs;
		left: 40px;
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		gap: $xxs;
		.btn-icon {
			border: 1px solid $colorBd;
			background: $colorWhite;
			box-shadow: $shadow;
		}
	}
	// double selector – fix old superadmin
	&__add#{&}__add {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 100%;
		margin-bottom: $xxs;
		padding: 8px $xs;
		border: 1px solid $colorBd;
		border-radius: $radius;
		background: $colorBgLighten;
		color: $colorTextLight;
		transition: color $t;
		box-shadow: $shadow;
		svg {
			width: 12px;
			height: 12px;
		}
		&--file {
			position: relative;
			input {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				opacity: 0;
				cursor: pointer;
			}
		}
	}
	&__name {
		position: absolute;
		right: 0;
		bottom: 0;
		left: 0;
		padding: 8px $xs;
		background: rgba($colorBd, 0.7);
		white-space: nowrap;
		text-overflow: ellipsis;
		overflow: hidden;
	}
	&__progress {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		z-index: 10;
		z-index: 0;
		width: 0;
		background-color: $colorBgLighten;
		transition: width $t linear, backgroundColor $t;
		.is-error & {
			background-color: rgba($colorRed, 0.2);
		}
		.is-success & {
			background-color: rgba($colorGreen, 0.2);
		}
	}

	&__item.sortable-drag {
		opacity: 0.25 !important;
	}

	// Variants
	&--selectable &__item {
		cursor: pointer;
	}
	&__item--selected &__name {
		background: rgba($colorPrimary, 0.8);
		color: $colorWhite;
	}

	// STATEs
	.hoverevents &__add:hover {
		color: $colorPrimary;
	}
	&__item--selected &__inner,
	.hoverevents &--selectable &__item:hover &__inner {
		border-color: $colorPrimary;
	}
	.hoverevents &--selectable &__item--selected:hover &__inner {
		border-color: $colorBd;
	}
}
