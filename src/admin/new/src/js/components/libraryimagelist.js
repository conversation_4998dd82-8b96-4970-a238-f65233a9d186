import { Controller } from 'stimulus';
import Sortable from 'sortable-dnd';
import { trigger } from '@superkoders/sk-tools/src/emmiter';
import { SnackbarTypes } from './snackbar';

export default class libraryimagelist extends Controller {
	static values = {
		url: String,
	};

	connect() {
		new Sortable(this.element, {
			handle: '[data-drag-handle], [data-drag-handle] *',
			draggable: '.b-imgs__item',
			ghostClass: 'sortable-drag',
			onDrop: (event) => {
				const droppedElement = document.elementFromPoint(event.event.clientX, event.event.clientY);
				const isListChild = this.element.contains(droppedElement);

				if (isListChild) {
					if (event.newIndex !== event.oldIndex) {
						this.updateListSort(event.node, event.to, event.newIndex);
					}
				} else {
					const treeLeaf = droppedElement.closest('li[data-id]');

					if (treeLeaf) {
						this.moveToFolder(treeLeaf.dataset.id, event.node.dataset.id, droppedElement.closest('a')?.innerText.trim());
					}
				}
			},
		});
	}

	moveToFolder(folderId, imageId, folderName) {
		if (folderId && imageId && window.confirm(`Opravdu chcete přesunout obrázek do složky${folderName ? ` ${folderName}` : ''}?`)) {
			const url = new URL(this.urlValue);

			const oldparams = new URLSearchParams(url.search);
			oldparams.append('imageId', imageId);
			oldparams.append('do', 'moveImageToFolder');
			oldparams.append('folderId', folderId);

			url.search = new URLSearchParams(oldparams);

			// move to new folder
			naja.makeRequest('GET', url.toString(), null, {
				history: false,
				loader: '[data-block-loader]',
			})
	.then(() =>
			trigger('notify', {
				message: `Obrázek přesunut do složky <strong>${folderName}</strong>`,
				type: SnackbarTypes.POSITIVE,
			}),
		)
			.catch(() =>
				trigger('notify', {
					message: 'Nepodařilo se přesunout obrázek',
					type: SnackbarTypes.NEGATIVE,
				}),
			);

	}
	}

	updateListSort(item, list, index) {
		const prevSibling = list.children[index - 1];
		const nextSibling = list.children[index + 1];
		const newPosition = nextSibling
			? parseInt(nextSibling.dataset.sort) - 1
			: prevSibling?.dataset.sort
				? parseInt(prevSibling.dataset.sort) + 1
				: null;
		const id = item.dataset.id;

		if (id && newPosition) {
			const url = new URL(this.urlValue);

			const oldparams = new URLSearchParams(url.search);
			oldparams.append('imageId', id);
			oldparams.append('do', 'moveImage');
			oldparams.append('position', newPosition);

			url.search = new URLSearchParams(oldparams);

			naja.makeRequest('GET', url.toString(), null, { history: false, loader: '[data-block-loader]' });
		} else {
			console.warn('Image move error: no id or position');
		}
	}
}
