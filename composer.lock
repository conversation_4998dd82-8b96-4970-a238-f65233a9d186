{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "a52ab3707ff52c393eca6002d0e6f7c4", "packages": [{"name": "a<PERSON><PERSON><PERSON><PERSON>/yasumi", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/azuyalabs/yasumi.git", "reference": "5fd99815e8bf480fd0e6b76527d5413767e98930"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/azuyalabs/yasumi/zipball/5fd99815e8bf480fd0e6b76527d5413767e98930", "reference": "5fd99815e8bf480fd0e6b76527d5413767e98930", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.4"}, "require-dev": {"friendsofphp/php-cs-fixer": "v2.19 | v3.5", "infection/infection": "^0.17 | ^0.26", "mikey179/vfsstream": "^1.6", "phan/phan": "^5.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5 | ^9.5", "vimeo/psalm": "^4.9"}, "suggest": {"ext-calendar": "For calculating the date of Easter"}, "type": "library", "autoload": {"psr-4": {"Yasumi\\": "src/<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sacha <PERSON>", "email": "<EMAIL>", "role": "Maintainer"}], "description": "The easy PHP Library for calculating holidays.", "homepage": "https://www.yasumi.dev", "keywords": ["Bank", "calculation", "calendar", "celebration", "date", "holiday", "holidays", "national", "time"], "support": {"docs": "https://www.yasumi.dev", "issues": "https://github.com/azuyalabs/yasumi/issues", "source": "https://github.com/azuyalabs/yasumi"}, "funding": [{"url": "https://www.buymeacoffee.com/sachatelgenhof", "type": "other"}], "time": "2022-01-30T07:43:17+00:00"}, {"name": "brick/math", "version": "0.10.2", "source": {"type": "git", "url": "https://github.com/brick/math.git", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/math/zipball/459f2781e1a08d52ee56b0b1444086e038561e3f", "reference": "459f2781e1a08d52ee56b0b1444086e038561e3f", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.0", "vimeo/psalm": "4.25.0"}, "type": "library", "autoload": {"psr-4": {"Brick\\Math\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Arbitrary-precision arithmetic library", "keywords": ["Arbitrary-precision", "BigInteger", "BigRational", "arithmetic", "bigdecimal", "bignum", "brick", "math"], "support": {"issues": "https://github.com/brick/math/issues", "source": "https://github.com/brick/math/tree/0.10.2"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2022-08-10T22:54:19+00:00"}, {"name": "brick/money", "version": "0.6.0", "source": {"type": "git", "url": "https://github.com/brick/money.git", "reference": "7074e1bd463f517fb78447dff63454f4b1523c1b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/brick/money/zipball/7074e1bd463f517fb78447dff63454f4b1523c1b", "reference": "7074e1bd463f517fb78447dff63454f4b1523c1b", "shasum": ""}, "require": {"brick/math": "~0.10.1", "php": "^7.4 || ^8.0"}, "require-dev": {"brick/varexporter": "~0.3.0", "ext-dom": "*", "ext-pdo": "*", "php-coveralls/php-coveralls": "^2.2", "phpunit/phpunit": "^9.4.3", "vimeo/psalm": "4.23.0"}, "suggest": {"ext-intl": "Required to format Money objects"}, "type": "library", "autoload": {"psr-4": {"Brick\\Money\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Money and currency library", "keywords": ["brick", "currency", "money"], "support": {"issues": "https://github.com/brick/money/issues", "source": "https://github.com/brick/money/tree/0.6.0"}, "funding": [{"url": "https://github.com/BenMorel", "type": "github"}], "time": "2022-08-01T23:09:55+00:00"}, {"name": "chris<PERSON><PERSON><PERSON>/deeply", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/chriskonnertz/DeepLy.git", "reference": "5fa08b7c7e740626c2a08bbcf35cdbdde57249ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/chriskonnertz/DeepLy/zipball/5fa08b7c7e740626c2a08bbcf35cdbdde57249ff", "reference": "5fa08b7c7e740626c2a08bbcf35cdbdde57249ff", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=8.0"}, "require-dev": {"phpunit/phpunit": "9.*"}, "type": "library", "extra": {"laravel": {"providers": ["ChrisKonnertz\\DeepLy\\Integrations\\Laravel\\DeepLyServiceProvider"], "aliases": {"DeepLy": "ChrisKonnertz\\DeepLy\\Integrations\\Laravel\\DeepLyFacade"}}}, "autoload": {"psr-0": {"ChrisKonnertz\\DeepLy": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "DeepLy is a PHP client for the DeepL.com translation API", "keywords": ["<PERSON><PERSON><PERSON>", "ai", "api", "client", "deepl", "deeply", "language", "library", "translate", "translation"], "support": {"issues": "https://github.com/chriskonnertz/DeepLy/issues", "source": "https://github.com/chriskonnertz/DeepLy/tree/v2.2.0"}, "time": "2023-07-06T14:00:18+00:00"}, {"name": "clue/stream-filter", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/clue/stream-filter.git", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/stream-filter/zipball/d6169430c7731d8509da7aecd0af756a5747b78e", "reference": "d6169430c7731d8509da7aecd0af756a5747b78e", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"Clue\\StreamFilter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A simple and modern approach to stream filtering in PHP", "homepage": "https://github.com/clue/php-stream-filter", "keywords": ["bucket brigade", "callback", "filter", "php_user_filter", "stream", "stream_filter_append", "stream_filter_register"], "support": {"issues": "https://github.com/clue/stream-filter/issues", "source": "https://github.com/clue/stream-filter/tree/v1.6.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-21T13:15:14+00:00"}, {"name": "composer/ca-bundle", "version": "1.3.5", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "74780ccf8c19d6acb8d65c5f39cd72110e132bbd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/74780ccf8c19d6acb8d65c5f39cd72110e132bbd", "reference": "74780ccf8c19d6acb8d65c5f39cd72110e132bbd", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-01-11T08:27:00+00:00"}, {"name": "contributte/apitte", "version": "v0.10.1", "source": {"type": "git", "url": "https://github.com/contributte/apitte.git", "reference": "2331453dd261da9f679caf2bed7bde7aa81e0704"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/apitte/zipball/2331453dd261da9f679caf2bed7bde7aa81e0704", "reference": "2331453dd261da9f679caf2bed7bde7aa81e0704", "shasum": ""}, "require": {"contributte/middlewares": "^0.10.0", "contributte/psr7-http-message": "~0.7.0", "doctrine/annotations": "^1.13", "ext-json": "*", "koriym/attributes": "^1.0.2", "nette/utils": "^3.2.7", "php": ">=7.4"}, "conflict": {"nette/tester": "<2.4.1"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"mockery/mockery": "^1.5.0", "nette/application": "^3.0.0", "nette/di": "^3.0.0", "nette/http": "^3.0.1", "nettrine/annotations": "^0.7.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.8.5", "phpstan/phpstan-deprecation-rules": "^1.1.0", "phpstan/phpstan-nette": "^1.1.0", "phpstan/phpstan-strict-rules": "^1.1.0", "psr/log": "^1.1|^2.0|^3.0", "symfony/console": "^5.4.3 || ^6.0.0", "symfony/validator": "^5.4.3 || ^6.0", "symfony/yaml": "^5.4.3 || ^6.0.0", "tracy/tracy": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Apitte\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "An opinionated and enjoyable API framework based on Nette Framework. Supporting content negotiation, debugging, middlewares, attributes, annotations and loving openapi/swagger.", "homepage": "https://github.com/contributte/apitte", "keywords": ["annotation", "api", "apitte", "http", "nette", "rest"], "support": {"issues": "https://github.com/contributte/apitte/issues", "source": "https://github.com/contributte/apitte/tree/v0.10.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-09-13T15:39:23+00:00"}, {"name": "contributte/application", "version": "v0.5.1", "source": {"type": "git", "url": "https://github.com/contributte/application.git", "reference": "2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/application/zipball/2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9", "reference": "2579ab2bc3b7c95ae32a2e664ac9a8cc038777f9", "shasum": ""}, "require": {"nette/application": "^3.0.0", "php": ">=7.2"}, "require-dev": {"nette/http": "~2.4.8 || ~3.0.0", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.9.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.1", "phpstan/phpstan-shim": "^0.11.2", "phpstan/phpstan-strict-rules": "^0.11.0", "psr/http-message": "~1.0.1", "tracy/tracy": "~2.6.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Application\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/application", "homepage": "https://github.com/contributte/application", "keywords": ["application", "component", "control", "nette", "presenter"], "support": {"issues": "https://github.com/contributte/application/issues", "source": "https://github.com/contributte/application/tree/v0.5.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-03-10T21:48:30+00:00"}, {"name": "contributte/console", "version": "v0.9.1", "source": {"type": "git", "url": "https://github.com/contributte/console.git", "reference": "549893573ba3cb81f476785763f48178b5166322"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console/zipball/549893573ba3cb81f476785763f48178b5166322", "reference": "549893573ba3cb81f476785763f48178b5166322", "shasum": ""}, "require": {"contributte/di": "^0.5.1", "php": ">=7.2", "symfony/console": "^4.2.9|^5.0.0"}, "require-dev": {"nette/http": "~3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "symfony/event-dispatcher": "^4.3.0 || ^5.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.10.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Best Symfony Console for Nette Framework", "homepage": "https://github.com/contributte/console", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console/issues", "source": "https://github.com/contributte/console/tree/v0.9.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-03-20T12:35:56+00:00"}, {"name": "contributte/console-extra", "version": "v0.7.1", "source": {"type": "git", "url": "https://github.com/contributte/console-extra.git", "reference": "29b59f60de741ae96a295691c342e0fa89062bdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/console-extra/zipball/29b59f60de741ae96a295691c342e0fa89062bdd", "reference": "29b59f60de741ae96a295691c342e0fa89062bdd", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "php": ">=7.2", "symfony/console": "^4.0.0 || ^5.0.0 || ^6.0.0"}, "conflict": {"nette/schema": "<1.0.1"}, "require-dev": {"contributte/console": "~0.7", "latte/latte": "^2.6.0", "nette/application": "^3.1.3", "nette/bootstrap": "^3.1.1", "nette/caching": "^3.1.1", "nette/security": "^3.1.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.13", "phpstan/phpstan": "^1.0.0", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-nette": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.0.0"}, "suggest": {"contributte/console": "Symfony\\Console for Nette"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.7.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Console\\Extra\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Nette-based console commands for latte, DIC, security, utils and many others", "homepage": "https://github.com/contributte/console-extra", "keywords": ["console", "nette", "symfony"], "support": {"issues": "https://github.com/contributte/console-extra/issues", "source": "https://github.com/contributte/console-extra/tree/v0.7.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-10T11:34:16+00:00"}, {"name": "contributte/di", "version": "v0.5.1", "source": {"type": "git", "url": "https://github.com/contributte/di.git", "reference": "534fdb5e85b4ae01f8f848fc4b752deb8458ed7c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/di/zipball/534fdb5e85b4ae01f8f848fc4b752deb8458ed7c", "reference": "534fdb5e85b4ae01f8f848fc4b752deb8458ed7c", "shasum": ""}, "require": {"nette/di": "~3.0.2", "nette/utils": "^3.0.3", "php": ">=7.2"}, "conflict": {"nette/di": "<3.0.0", "nette/schema": "<1.1.0"}, "require-dev": {"nette/bootstrap": "~3.0.0", "nette/robot-loader": "^3.0.4", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12"}, "suggest": {"nette/reflection": "to use AutoloadExtension[CompilerExtension]", "nette/robot-loader": "to use AutoloadExtension[CompilerExtension]"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.5.x-dev"}}, "autoload": {"psr-4": {"Contributte\\DI\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Extra contrib to nette/di", "homepage": "https://github.com/contributte/di", "keywords": ["dependency", "inject", "nette"], "support": {"issues": "https://github.com/contributte/di/issues", "source": "https://github.com/contributte/di/tree/v0.5.1"}, "time": "2020-12-26T17:02:42+00:00"}, {"name": "contributte/elastica", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/contributte/elastica.git", "reference": "98b73392a5d53fc900aed1fc2bb377f336aebf68"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/elastica/zipball/98b73392a5d53fc900aed1fc2bb377f336aebf68", "reference": "98b73392a5d53fc900aed1fc2bb377f336aebf68", "shasum": ""}, "require": {"nette/di": "^3.0", "nette/utils": "^3.0", "php": ">=7.2", "ruflin/elastica": "^7.0"}, "conflict": {"nette/schema": "<1.2.0"}, "require-dev": {"nette/bootstrap": "^3.0", "nette/http": "^3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^7.0|8.0|^9.0", "tracy/tracy": "^2.6"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Elastica implementation for Nette Framework", "homepage": "https://github.com/contributte/elastica", "keywords": ["elastic", "elastica", "elasticsearch", "es", "nette", "search"], "support": {"issues": "https://github.com/contributte/elastica/issues", "source": "https://github.com/contributte/elastica/tree/master"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-10-06T07:46:16+00:00"}, {"name": "contributte/logging", "version": "v0.6.2", "source": {"type": "git", "url": "https://github.com/contributte/logging.git", "reference": "a10912095af3646d098353cac13598ad65a5b8df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/logging/zipball/a10912095af3646d098353cac13598ad65a5b8df", "reference": "a10912095af3646d098353cac13598ad65a5b8df", "shasum": ""}, "require": {"php": ">=7.2", "tracy/tracy": "~2.5.5|~2.6.2|~2.7.0|~2.8.0|~2.9.0"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"ext-json": "*", "nette/di": "^3.0.0", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "sentry/sdk": "^3.0.0"}, "suggest": {"nette/di": "to use TracyLoggingExtension", "sentry/sdk": "to use SentryLoggingExtension"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Logging\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Plug-in support logging for Tracy / Nette Framework", "homepage": "https://github.com/contributte/logging", "keywords": ["logging", "monolog", "nette", "plugins", "tracy"], "support": {"issues": "https://github.com/contributte/logging/issues", "source": "https://github.com/contributte/logging/tree/v0.6.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-12-30T17:29:18+00:00"}, {"name": "contributte/middlewares", "version": "v0.10.2", "source": {"type": "git", "url": "https://github.com/contributte/middlewares.git", "reference": "c875ed28e2da0b369bea9ee627ab8c1d9757673e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/middlewares/zipball/c875ed28e2da0b369bea9ee627ab8c1d9757673e", "reference": "c875ed28e2da0b369bea9ee627ab8c1d9757673e", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "contributte/psr7-http-message": "^0.7.0", "php": ">=7.2"}, "require-dev": {"nette/application": "^3.0.0", "nette/http": "^3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "psr/log": "^1.0", "tracy/tracy": "^2.7.0"}, "suggest": {"nette/application": "to use PresenterMiddleware", "nette/http": "to use NetteMiddlewareExtension & NetteMiddlewareApplication", "tracy/tracy": "to use TracyMiddleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.11.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Middlewares\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Middleware / Relay / PSR-7 support to Nette Framework", "homepage": "https://github.com/contributte/middlewares", "keywords": ["<PERSON><PERSON>", "middleware", "nette"], "support": {"issues": "https://github.com/contributte/middlewares/issues", "source": "https://github.com/contributte/middlewares/tree/v0.10.2"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-04-01T19:22:48+00:00"}, {"name": "contributte/monolog", "version": "v0.5.0", "source": {"type": "git", "url": "https://github.com/contributte/monolog.git", "reference": "a47a3f634c7d9ba21f0923d3479f38a459767182"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/monolog/zipball/a47a3f634c7d9ba21f0923d3479f38a459767182", "reference": "a47a3f634c7d9ba21f0923d3479f38a459767182", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "monolog/monolog": "^2.0.0", "nette/utils": "^3.0.0", "php": ">=7.2"}, "conflict": {"tracy/tracy": "<2.6.2"}, "require-dev": {"ninjify/qa": "^0.10.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "phpunit/phpunit": "^8.1.3", "tracy/tracy": "~2.6.2 || ~2.7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Monolog\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Monolog integration into Nette Framework", "homepage": "https://github.com/contributte/monolog", "keywords": ["logging", "monolog", "nette"], "support": {"issues": "https://github.com/contributte/monolog/issues", "source": "https://github.com/contributte/monolog/tree/v0.5.0"}, "time": "2020-12-10T14:31:08+00:00"}, {"name": "contributte/psr7-http-message", "version": "v0.7.1", "source": {"type": "git", "url": "https://github.com/contributte/psr7-http-message.git", "reference": "7e7d2176b531cf28a1efddca95ffaade516efab3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/psr7-http-message/zipball/7e7d2176b531cf28a1efddca95ffaade516efab3", "reference": "7e7d2176b531cf28a1efddca95ffaade516efab3", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7.0", "nette/utils": "^3.0.0", "php": ">=7.2"}, "conflict": {"nette/di": "<3.0.7", "nette/http": "<3.0.5"}, "require-dev": {"nette/application": "^3.0.0", "nette/di": "^3.0.7", "nette/http": "^3.0.5", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12"}, "suggest": {"nette/application": "to use $request->withApplicationRequest[Nette\\Application\\Request]", "nette/di": "to use Psr7HttpExtension[CompilerExtension]", "nette/http": "to use $request->withHttpRequest[Nette\\Http\\Request], to use Psr7RequestFactory, to use Psr7ServerRequestFactory"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Psr7\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "PSR-7 (HTTP Message Interface) to Nette Framework", "homepage": "https://github.com/contributte/psr7-http-message", "keywords": ["http", "message", "nette", "psr7", "request", "response"], "support": {"issues": "https://github.com/contributte/psr7-http-message/issues", "source": "https://github.com/contributte/psr7-http-message/tree/v0.7.1"}, "time": "2021-01-04T17:56:43+00:00"}, {"name": "contributte/redis", "version": "v0.5.3", "source": {"type": "git", "url": "https://github.com/contributte/redis.git", "reference": "7d404e50f65919b982ca3703245ed714d582c078"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/redis/zipball/7d404e50f65919b982ca3703245ed714d582c078", "reference": "7d404e50f65919b982ca3703245ed714d582c078", "shasum": ""}, "require": {"ext-json": "*", "nette/di": "^2.4.17 || ^3.0.1", "php": ">=7.2", "predis/predis": "^1.1.6 || ^2.0.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "nette/caching": "^2.5.0 || ^3.1.3", "nette/http": "^2.4.0 || ^3.0.1", "ninjify/nunjuck": "^0.4", "ninjify/qa": "^0.12", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-nette": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "tracy/tracy": "^2.7.0"}, "suggest": {"ext-igbinary": "For better serialization"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.6.x-dev"}}, "autoload": {"psr-4": {"Contributte\\Redis\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Redis client integration into Nette framework", "homepage": "https://github.com/contributte/redis", "keywords": ["cache", "nette", "predis", "redis"], "support": {"issues": "https://github.com/contributte/redis/issues", "source": "https://github.com/contributte/redis/tree/v0.5.3"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-09-28T11:13:00+00:00"}, {"name": "contributte/validator", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/contributte/validator.git", "reference": "9e104d9856a7533e89721e35a7eeb900d03080c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/validator/zipball/9e104d9856a7533e89721e35a7eeb900d03080c2", "reference": "9e104d9856a7533e89721e35a7eeb900d03080c2", "shasum": ""}, "require": {"nette/di": "^3.0.1", "php": ">=7.4", "symfony/cache": "^5.0 || ^6.0", "symfony/config": "^5.0 || ^6.0", "symfony/validator": "^5.2 || ^6.0"}, "require-dev": {"doctrine/annotations": "^1.8", "doctrine/cache": "^1.10", "nette/bootstrap": "^3.0", "nette/tester": "^2.4", "ninjify/nunjuck": "^0.3.0", "ninjify/qa": "^0.12", "phpstan/phpstan": "^0.12", "phpstan/phpstan-deprecation-rules": "^0.12", "phpstan/phpstan-nette": "^0.12", "phpstan/phpstan-strict-rules": "^0.12", "symfony/translation": "^5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Contributte\\Validator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://jiripudil.cz"}], "description": "Symfony/Validator integration for Nette Framework.", "homepage": "https://github.com/contributte/validator", "keywords": ["nette", "symfony", "validator"], "support": {"issues": "https://github.com/contributte/validator/issues", "source": "https://github.com/contributte/validator/tree/v1.1.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2021-12-30T09:59:15+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.2", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e9969cfc0796e6dea9b4e52f77f18e1065212871"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e9969cfc0796e6dea9b4e52f77f18e1065212871", "reference": "e9969cfc0796e6dea9b4e52f77f18e1065212871", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.2"}, "time": "2022-01-25T19:21:20+00:00"}, {"name": "doctrine/annotations", "version": "1.13.2", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "5b668aef16090008790395c02c893b1ba13f7e08"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/5b668aef16090008790395c02c893b1ba13f7e08", "reference": "5b668aef16090008790395c02c893b1ba13f7e08", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^8.0 || ^9.1.5", "symfony/cache": "^4.4 || ^5.2"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.13.2"}, "time": "2021-08-05T19:00:23+00:00"}, {"name": "doctrine/cache", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "56cd022adb5514472cb144c087393c1821911d09"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/56cd022adb5514472cb144c087393c1821911d09", "reference": "56cd022adb5514472cb144c087393c1821911d09", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "predis/predis": "~1.0", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.13.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:06:54+00:00"}, {"name": "doctrine/dbal", "version": "3.4.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "94e016428884227245fb1219e0de7d8b86ca16d7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/94e016428884227245fb1219e0de7d8b86ca16d7", "reference": "94e016428884227245fb1219e0de7d8b86ca16d7", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1.0", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "9.0.0", "jetbrains/phpstorm-stubs": "2022.1", "phpstan/phpstan": "1.8.2", "phpstan/phpstan-strict-rules": "^1.3", "phpunit/phpunit": "9.5.21", "psalm/plugin-phpunit": "0.17.0", "squizlabs/php_codesniffer": "3.7.1", "symfony/cache": "^5.4|^6.0", "symfony/console": "^4.4|^5.4|^6.0", "vimeo/psalm": "4.24.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.4.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2022-08-16T18:37:46+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/eb2ecf80e3093e8f3c2769ac838e27d8ede8e683", "reference": "eb2ecf80e3093e8f3c2769ac838e27d8ede8e683", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "~1.4.10 || ^1.5.4", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.22"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.2"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2022-07-27T22:18:11+00:00"}, {"name": "doctrine/lexer", "version": "1.2.3", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/c268e882d4dbdd85e36e4ad69e02dc284f89d229", "reference": "c268e882d4dbdd85e36e4ad69e02dc284f89d229", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "vimeo/psalm": "^4.11"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.3"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-02-28T11:07:21+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v7.17.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/1890f9d7fde076b5a3ddcf579a802af05b2e781b", "reference": "1890f9d7fde076b5a3ddcf579a802af05b2e781b", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2|^3"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0", "LGPL-2.1-only"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v7.17.0"}, "time": "2022-02-03T13:40:04+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.0.1"}, "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/92b8161404ab1ad84059ebed41d9f757e897ce74", "reference": "92b8161404ab1ad84059ebed41d9f757e897ce74", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.0"}, "time": "2021-11-16T11:51:30+00:00"}, {"name": "firebase/php-jwt", "version": "v6.1.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/fbb2967a3a68b07e37678c00c0cf51165051495f", "reference": "fbb2967a3a68b07e37678c00c0cf51165051495f", "shasum": ""}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"phpunit/phpunit": "^7.5||9.5"}, "suggest": {"paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.1.0"}, "time": "2022-03-23T18:26:04+00:00"}, {"name": "fmasa/messenger", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/fmasa/messenger.git", "reference": "9b3b3d0aa65091fde3baa1f8cd1025cec63df8e0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fmasa/messenger/zipball/9b3b3d0aa65091fde3baa1f8cd1025cec63df8e0", "reference": "9b3b3d0aa65091fde3baa1f8cd1025cec63df8e0", "shasum": ""}, "require": {"nette/di": "^3.0.1", "nette/schema": "^1.0.3", "php": "^7.4 || ^8.0", "psr/container": "^1.0", "symfony/console": "^4.4|^5.0", "symfony/event-dispatcher": "^4.4|^5.0", "symfony/messenger": "^5.3", "tracy/tracy": "^2.6"}, "require-dev": {"doctrine/coding-standard": "^8.1", "mockery/mockery": "^1.2", "nette/bootstrap": "^3.0", "phpstan/phpstan": "^1.2.0", "phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"psr-4": {"Fmasa\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Symfony/Messenger integration into Nette Framework", "keywords": ["<PERSON>", "command bus", "event bus", "message bus", "nette", "symfony", "symfony-messenger"], "support": {"issues": "https://github.com/fmasa/messenger/issues", "source": "https://github.com/fmasa/messenger/tree/2.0.0"}, "time": "2022-01-31T12:22:07+00:00"}, {"name": "gopay/payments-sdk-php", "version": "v1.5.3", "source": {"type": "git", "url": "https://github.com/gopaycommunity/gopay-php-api.git", "reference": "1b12a9e335475c70010754e3d9a1d39d6a2d2dc8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/gopaycommunity/gopay-php-api/zipball/1b12a9e335475c70010754e3d9a1d39d6a2d2dc8", "reference": "1b12a9e335475c70010754e3d9a1d39d6a2d2dc8", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.2.1|7.*", "php": ">=7.4"}, "require-dev": {"hamcrest/hamcrest-php": "*", "phpspec/prophecy": "~1.0", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "9.3.7"}, "type": "library", "autoload": {"files": ["factory.php"], "psr-4": {"GoPay\\": "src/"}, "classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "GoPay", "homepage": "https://github.com/gopaycommunity/gopay-php-api/contributors"}], "description": "GoPay's PHP SDK for Payments REST API", "keywords": ["api", "gopay", "payments", "rest", "sdk"], "support": {"issues": "https://github.com/gopaycommunity/gopay-php-api/issues", "source": "https://github.com/gopaycommunity/gopay-php-api/tree/v1.5.3"}, "time": "2022-03-30T13:12:44+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.4.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "reference": "1dd98b0564cb3f6bd16ce683cb755f94c10fbd82", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.5 || ^9.3.5", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.4-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.4.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-06-20T22:16:13+00:00"}, {"name": "guzzlehttp/promises", "version": "1.5.1", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "reference": "fe752aedc9fd8fcca3fe7ad05d419d32998a06da", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2021-10-22T20:56:57+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.9.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "reference": "e98e3e6d4f86621a9b75f623996e6bbdeb4b9318", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.9.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-06-20T21:43:03+00:00"}, {"name": "heureka/overeno-zakazniky", "version": "v4.0.0", "source": {"type": "git", "url": "https://github.com/heureka/overeno-zakazniky.git", "reference": "ef105b5f5e683313c741175af4639109f2b062d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/heureka/overeno-zakazniky/zipball/ef105b5f5e683313c741175af4639109f2b062d5", "reference": "ef105b5f5e683313c741175af4639109f2b062d5", "shasum": ""}, "require": {"php": ">=7.3"}, "require-dev": {"mockery/mockery": "~1.4.3", "phpunit/phpunit": "~9.5.0"}, "suggest": {"ext-curl": "Simplifies the library usage (you don't have to provide your own requester)"}, "type": "library", "autoload": {"psr-4": {"Heureka\\": ["src/", "tests"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Heureka.cz", "email": "<EMAIL>"}], "description": "Heureka 'Ov<PERSON><PERSON>eno zákazníky' (ShopCertification) service API implementation", "support": {"issues": "https://github.com/heureka/overeno-zakazniky/issues", "source": "https://github.com/heureka/overeno-zakazniky/tree/v4.0.0"}, "time": "2021-05-13T08:43:44+00:00"}, {"name": "http-interop/http-factory-guzzle", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/http-interop/http-factory-guzzle.git", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/http-interop/http-factory-guzzle/zipball/8f06e92b95405216b237521cc64c804dd44c4a81", "reference": "8f06e92b95405216b237521cc64c804dd44c4a81", "shasum": ""}, "require": {"guzzlehttp/psr7": "^1.7||^2.0", "php": ">=7.3", "psr/http-factory": "^1.0"}, "provide": {"psr/http-factory-implementation": "^1.0"}, "require-dev": {"http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^9.5"}, "suggest": {"guzzlehttp/psr7": "Includes an HTTP factory starting in version 2.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Factory\\Guzzle\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "An HTTP Factory using Guzzle PSR7", "keywords": ["factory", "http", "psr-17", "psr-7"], "support": {"issues": "https://github.com/http-interop/http-factory-guzzle/issues", "source": "https://github.com/http-interop/http-factory-guzzle/tree/1.2.0"}, "time": "2021-07-21T13:50:14+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.111", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "d572ed4a65a70a2d2871dc5137c9c5b7e69745ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/d572ed4a65a70a2d2871dc5137c9c5b7e69745ab", "reference": "d572ed4a65a70a2d2871dc5137c9c5b7e69745ab", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.111"}, "time": "2022-03-15T22:19:01+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/ae547e455a3d8babd07b96966b17d7fd21d9c6af", "reference": "ae547e455a3d8babd07b96966b17d7fd21d9c6af", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^0.12.66", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.5"}, "time": "2021-10-08T21:21:46+00:00"}, {"name": "koriym/attributes", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/koriym/Koriym.Attributes.git", "reference": "c64c79d582a935b1eea0675032757a0621a2540e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/koriym/Koriym.Attributes/zipball/c64c79d582a935b1eea0675032757a0621a2540e", "reference": "c64c79d582a935b1eea0675032757a0621a2540e", "shasum": ""}, "require": {"doctrine/annotations": "^1.11", "php": "^7.2 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4", "ext-pdo": "*", "phpunit/phpunit": "^8.5.24 | ^9.5"}, "suggest": {"koriym/param-reader": "An attribute/annotation reader for parameters"}, "type": "library", "autoload": {"psr-4": {"Koriym\\Attributes\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An annotation/attribute reader", "keywords": ["annotation", "attribute"], "support": {"issues": "https://github.com/koriym/Koriym.Attributes/issues", "source": "https://github.com/koriym/Koriym.Attributes/tree/1.0.4"}, "time": "2022-03-09T07:52:32+00:00"}, {"name": "latte/latte", "version": "v3.0.0", "source": {"type": "git", "url": "https://github.com/nette/latte.git", "reference": "0a3f7fb3f930733c5cbc8d2f67943006d6ab2652"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/latte/zipball/0a3f7fb3f930733c5cbc8d2f67943006d6ab2652", "reference": "0a3f7fb3f930733c5cbc8d2f67943006d6ab2652", "shasum": ""}, "require": {"ext-json": "*", "ext-tokenizer": "*", "php": ">=8.0 <8.2"}, "conflict": {"nette/application": "<2.4.1"}, "require-dev": {"nette/php-generator": "^3.3.4", "nette/tester": "^2.0", "nette/utils": "^3.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "suggest": {"ext-fileinfo": "to use filter |datastream", "ext-iconv": "to use filters |reverse, |substring", "ext-mbstring": "to use filters like lower, upper, capitalize, ...", "nette/php-generator": "to use tag {templatePrint}", "nette/utils": "to use filter |webalize"}, "bin": ["bin/latte-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "☕ Latte: the intuitive and fast template engine for those who want the most secure PHP sites. Introduces context-sensitive escaping.", "homepage": "https://latte.nette.org", "keywords": ["context-sensitive", "engine", "escaping", "html", "nette", "security", "template", "twig"], "support": {"issues": "https://github.com/nette/latte/issues", "source": "https://github.com/nette/latte/tree/v3.0.0"}, "time": "2022-05-17T20:14:20+00:00"}, {"name": "league/csv", "version": "9.8.0", "source": {"type": "git", "url": "https://github.com/thephpleague/csv.git", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/csv/zipball/9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "reference": "9d2e0265c5d90f5dd601bc65ff717e05cec19b47", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": "^7.4 || ^8.0"}, "require-dev": {"ext-curl": "*", "ext-dom": "*", "friendsofphp/php-cs-fixer": "^v3.4.0", "phpstan/phpstan": "^1.3.0", "phpstan/phpstan-phpunit": "^1.0.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.5.11"}, "suggest": {"ext-dom": "Required to use the XMLConverter and or the HTMLConverter classes", "ext-iconv": "Needed to ease transcoding CSV using iconv stream filters"}, "type": "library", "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"League\\Csv\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "description": "CSV data manipulation made easy in PHP", "homepage": "https://csv.thephpleague.com", "keywords": ["convert", "csv", "export", "filter", "import", "read", "transform", "write"], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"url": "https://github.com/sponsors/nyamsprod", "type": "github"}], "time": "2022-01-04T00:13:07+00:00"}, {"name": "league/oauth2-client", "version": "2.6.1", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-client.git", "reference": "2334c249907190c132364f5dae0287ab8666aa19"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-client/zipball/2334c249907190c132364f5dae0287ab8666aa19", "reference": "2334c249907190c132364f5dae0287ab8666aa19", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.0 || ^7.0", "paragonie/random_compat": "^1 || ^2 || ^9.99", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"mockery/mockery": "^1.3.5", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpunit/phpunit": "^5.7 || ^6.0 || ^9.5", "squizlabs/php_codesniffer": "^2.3 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.0.x-dev"}}, "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.alexbilbie.com", "role": "Developer"}, {"name": "<PERSON>", "homepage": "https://github.com/shadowhand", "role": "Contributor"}], "description": "OAuth 2.0 Client Library", "keywords": ["Authentication", "SSO", "authorization", "identity", "idp", "o<PERSON>h", "oauth2", "single sign on"], "support": {"issues": "https://github.com/thephpleague/oauth2-client/issues", "source": "https://github.com/thephpleague/oauth2-client/tree/2.6.1"}, "time": "2021-12-22T16:42:49+00:00"}, {"name": "league/oauth2-google", "version": "4.0.0", "source": {"type": "git", "url": "https://github.com/thephpleague/oauth2-google.git", "reference": "db6d8ad67cdd7d014a1e5dd5c204a319a966de86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/oauth2-google/zipball/db6d8ad67cdd7d014a1e5dd5c204a319a966de86", "reference": "db6d8ad67cdd7d014a1e5dd5c204a319a966de86", "shasum": ""}, "require": {"league/oauth2-client": "^2.0", "php": ">=7.3"}, "require-dev": {"eloquent/phony-phpunit": "^6.0 || ^7.1", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.0"}, "type": "library", "autoload": {"psr-4": {"League\\OAuth2\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://shadowhand.me"}], "description": "Google OAuth 2.0 Client Provider for The PHP League OAuth2-Client", "keywords": ["Authentication", "authorization", "client", "google", "o<PERSON>h", "oauth2"], "support": {"issues": "https://github.com/thephpleague/oauth2-google/issues", "source": "https://github.com/thephpleague/oauth2-google/tree/4.0.0"}, "time": "2021-03-04T21:12:06+00:00"}, {"name": "marc-mabe/php-enum", "version": "v4.6.1", "source": {"type": "git", "url": "https://github.com/marc-mabe/php-enum.git", "reference": "2eb0a50244f89150d7218cbc8a7f0f56d4520028"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/marc-mabe/php-enum/zipball/2eb0a50244f89150d7218cbc8a7f0f56d4520028", "reference": "2eb0a50244f89150d7218cbc8a7f0f56d4520028", "shasum": ""}, "require": {"ext-reflection": "*", "php": "^7.1 | ^8.0"}, "require-dev": {"phpbench/phpbench": "^0.16.10 || ^1.0.4", "phpstan/phpstan": "^1.3.1", "phpunit/phpunit": "^7.5.20 | ^8.5.22 | ^9.5.11", "vimeo/psalm": "^4.17.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.6-dev", "dev-3.x": "3.2-dev"}}, "autoload": {"psr-4": {"MabeEnum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://mabe.berlin/", "role": "Lead"}], "description": "Simple and fast implementation of enumerations with native PHP", "homepage": "https://github.com/marc-mabe/php-enum", "keywords": ["enum", "enum-map", "enum-set", "enumeration", "enumerator", "enummap", "enumset", "map", "set", "type", "type-hint", "<PERSON><PERSON>t"], "support": {"issues": "https://github.com/marc-mabe/php-enum/issues", "source": "https://github.com/marc-mabe/php-enum/tree/v4.6.1"}, "time": "2022-02-13T06:05:42+00:00"}, {"name": "monolog/monolog", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "d7fd7450628561ba697b7097d86db72662f54aef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/d7fd7450628561ba697b7097d86db72662f54aef", "reference": "d7fd7450628561ba697b7097d86db72662f54aef", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1 || ^2.0 || ^3.0"}, "provide": {"psr/log-implementation": "1.0.0 || 2.0.0 || 3.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4 || ^3", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.91", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3 || ^2 || ^3", "ruflin/elastica": ">=0.90@dev", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-curl": "Required to send log messages using the IFTTTHandler, the LogglyHandler, the SendGridHandler, the SlackWebhookHandler or the TelegramBotHandler", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "ext-openssl": "Required to send log messages using SSL", "ext-sockets": "Allow sending log messages to a Syslog server (via UDP driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.4.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-03-14T12:44:37+00:00"}, {"name": "mpdf/mpdf", "version": "v8.0.17", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "5f64118317c8145c0abc606b310aa0a66808398a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/5f64118317c8145c0abc606b310aa0a66808398a", "reference": "5f64118317c8145c0abc606b310aa0a66808398a", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0 || ~8.1.0", "psr/log": "^1.0 || ^2.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "^2.4", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2022-01-20T10:51:40+00:00"}, {"name": "myclabs/deep-copy", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/14daed4296fae74d9e3201d2c4925d1acb7aa614", "reference": "14daed4296fae74d9e3201d2c4925d1acb7aa614", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/collections": "<1.6.8", "doctrine/common": "<2.13.3 || >=3,<3.2.2"}, "require-dev": {"doctrine/collections": "^1.6.8", "doctrine/common": "^2.13.3 || ^3.2.2", "phpunit/phpunit": "^7.5.20 || ^8.5.23 || ^9.5.13"}, "type": "library", "autoload": {"files": ["src/DeepCopy/deep_copy.php"], "psr-4": {"DeepCopy\\": "src/DeepCopy/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.11.0"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2022-03-03T13:19:32+00:00"}, {"name": "nette/application", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/nette/application.git", "reference": "2cbc947d057e4ce28ea0296923def3d45ce60885"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/application/zipball/2cbc947d057e4ce28ea0296923def3d45ce60885", "reference": "2cbc947d057e4ce28ea0296923def3d45ce60885", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.0.2", "nette/routing": "^3.0.2", "nette/utils": "^3.2.1", "php": ">=7.2"}, "conflict": {"latte/latte": "<2.7.1 || >=3.1", "nette/caching": "<3.1", "nette/di": "<3.0.7", "nette/forms": "<3.0", "nette/schema": "<1.2", "tracy/tracy": "<2.5"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0", "mockery/mockery": "^1.0", "nette/di": "^v3.0", "nette/forms": "^3.0", "nette/robot-loader": "^3.2", "nette/security": "^3.0", "nette/tester": "^2.3.1", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"latte/latte": "Allows using Latte in templates", "nette/forms": "Allows to use Nette\\Application\\UI\\Form"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🏆 Nette Application: a full-stack component-based MVC kernel for PHP that helps you write powerful and modern web applications. Write less, have cleaner code and your work will bring you joy.", "homepage": "https://nette.org", "keywords": ["Forms", "component-based", "control", "framework", "mvc", "mvp", "nette", "presenter", "routing", "seo"], "support": {"issues": "https://github.com/nette/application/issues", "source": "https://github.com/nette/application/tree/v3.1.6"}, "time": "2022-05-17T19:36:26+00:00"}, {"name": "nette/bootstrap", "version": "v3.1.2", "source": {"type": "git", "url": "https://github.com/nette/bootstrap.git", "reference": "3ab4912a08af0c16d541c3709935c3478b5ee090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/bootstrap/zipball/3ab4912a08af0c16d541c3709935c3478b5ee090", "reference": "3ab4912a08af0c16d541c3709935c3478b5ee090", "shasum": ""}, "require": {"nette/di": "^3.0.5", "nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"tracy/tracy": "<2.6"}, "require-dev": {"latte/latte": "^2.8", "nette/application": "^3.1", "nette/caching": "^3.0", "nette/database": "^3.0", "nette/forms": "^3.0", "nette/http": "^3.0", "nette/mail": "^3.0", "nette/robot-loader": "^3.0", "nette/safe-stream": "^2.2", "nette/security": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.6"}, "suggest": {"nette/robot-loader": "to use Configurator::createRobotLoader()", "tracy/tracy": "to use Configurator::enableTracy()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🅱  Nette Bootstrap: the simple way to configure and bootstrap your Nette application.", "homepage": "https://nette.org", "keywords": ["bootstrapping", "configurator", "nette"], "support": {"issues": "https://github.com/nette/bootstrap/issues", "source": "https://github.com/nette/bootstrap/tree/v3.1.2"}, "time": "2021-11-24T16:51:46+00:00"}, {"name": "nette/caching", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/nette/caching.git", "reference": "e1e38105956bb631e2295ef7a2fdef83485238e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/caching/zipball/e1e38105956bb631e2295ef7a2fdef83485238e9", "reference": "e1e38105956bb631e2295ef7a2fdef83485238e9", "shasum": ""}, "require": {"nette/finder": "^2.4 || ^3.0", "nette/utils": "^2.4 || ^3.0", "php": ">=7.2 <8.3"}, "require-dev": {"latte/latte": "^2.11 || ^3.0", "nette/di": "^v3.0", "nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-pdo_sqlite": "to use SQLiteStorage or SQLiteJournal"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⏱ Nette Caching: library with easy-to-use API and many cache backends.", "homepage": "https://nette.org", "keywords": ["cache", "journal", "memcached", "nette", "sqlite"], "support": {"issues": "https://github.com/nette/caching/issues", "source": "https://github.com/nette/caching/tree/v3.1.4"}, "time": "2022-10-18T23:27:44+00:00"}, {"name": "nette/component-model", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/nette/component-model.git", "reference": "20a39df12009029c7e425bc5e0439ee4ab5304af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/component-model/zipball/20a39df12009029c7e425bc5e0439ee4ab5304af", "reference": "20a39df12009029c7e425bc5e0439ee4ab5304af", "shasum": ""}, "require": {"nette/utils": "^2.5 || ^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "⚛ Nette Component Model", "homepage": "https://nette.org", "keywords": ["components", "nette"], "support": {"issues": "https://github.com/nette/component-model/issues", "source": "https://github.com/nette/component-model/tree/v3.0.2"}, "time": "2021-08-25T14:52:12+00:00"}, {"name": "nette/di", "version": "v3.0.13", "source": {"type": "git", "url": "https://github.com/nette/di.git", "reference": "9878f2958a0a804b08430dbc719a52e493022739"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/di/zipball/9878f2958a0a804b08430dbc719a52e493022739", "reference": "9878f2958a0a804b08430dbc719a52e493022739", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/neon": "^3.3 || ^4.0", "nette/php-generator": "^3.5.4 || ^4.0", "nette/robot-loader": "^3.2", "nette/schema": "^1.1", "nette/utils": "^3.1.6", "php": ">=7.1 <8.2"}, "conflict": {"nette/bootstrap": "<3.0"}, "require-dev": {"nette/tester": "^2.2", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "💎 Nette Dependency Injection Container: Flexible, compiled and full-featured DIC with perfectly usable autowiring and support for all new PHP features.", "homepage": "https://nette.org", "keywords": ["compiled", "di", "dic", "factory", "ioc", "nette", "static"], "support": {"issues": "https://github.com/nette/di/issues", "source": "https://github.com/nette/di/tree/v3.0.13"}, "time": "2022-03-10T02:43:04+00:00"}, {"name": "nette/finder", "version": "v2.5.3", "source": {"type": "git", "url": "https://github.com/nette/finder.git", "reference": "64dc25b7929b731e72a1bc84a9e57727f5d5d3e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/finder/zipball/64dc25b7929b731e72a1bc84a9e57727f5d5d3e8", "reference": "64dc25b7929b731e72a1bc84a9e57727f5d5d3e8", "shasum": ""}, "require": {"nette/utils": "^2.4 || ^3.0", "php": ">=7.1"}, "conflict": {"nette/nette": "<2.2"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔍 Nette Finder: find files and directories with an intuitive API.", "homepage": "https://nette.org", "keywords": ["filesystem", "glob", "iterator", "nette"], "support": {"issues": "https://github.com/nette/finder/issues", "source": "https://github.com/nette/finder/tree/v2.5.3"}, "time": "2021-12-12T17:43:24+00:00"}, {"name": "nette/forms", "version": "v3.1.7", "source": {"type": "git", "url": "https://github.com/nette/forms.git", "reference": "fe2109ce8b77846a5f664bc412c7cf3008f63074"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/forms/zipball/fe2109ce8b77846a5f664bc412c7cf3008f63074", "reference": "fe2109ce8b77846a5f664bc412c7cf3008f63074", "shasum": ""}, "require": {"nette/component-model": "^3.0", "nette/http": "^3.1", "nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"latte/latte": ">=3.1", "nette/di": "<3.0-stable"}, "require-dev": {"latte/latte": "^2.10.2 || ^3.0", "nette/application": "^3.0", "nette/di": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📝 Nette Forms: generating, validating and processing secure forms in PHP. Handy API, fully customizable, server & client side validation and mature design.", "homepage": "https://nette.org", "keywords": ["Forms", "bootstrap", "csrf", "javascript", "nette", "validation"], "support": {"issues": "https://github.com/nette/forms/issues", "source": "https://github.com/nette/forms/tree/v3.1.7"}, "time": "2022-05-12T15:30:17+00:00"}, {"name": "nette/http", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/nette/http.git", "reference": "65bfe68f9c611e7cd1935a5f794a560c52e4614f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/http/zipball/65bfe68f9c611e7cd1935a5f794a560c52e4614f", "reference": "65bfe68f9c611e7cd1935a5f794a560c52e4614f", "shasum": ""}, "require": {"nette/utils": "^3.1", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0.3", "nette/schema": "<1.2"}, "require-dev": {"nette/di": "^3.0", "nette/security": "^3.0", "nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of uploaded files"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🌐 Nette Http: abstraction for HTTP request, response and session. Provides careful data sanitization and utility for URL and cookies manipulation.", "homepage": "https://nette.org", "keywords": ["cookies", "http", "nette", "proxy", "request", "response", "security", "session", "url"], "support": {"issues": "https://github.com/nette/http/issues", "source": "https://github.com/nette/http/tree/v3.1.6"}, "time": "2022-04-02T16:05:07+00:00"}, {"name": "nette/mail", "version": "v3.1.8", "source": {"type": "git", "url": "https://github.com/nette/mail.git", "reference": "69b43ae9a5c63ff68804531ef0113c372c676ce6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/mail/zipball/69b43ae9a5c63ff68804531ef0113c372c676ce6", "reference": "69b43ae9a5c63ff68804531ef0113c372c676ce6", "shasum": ""}, "require": {"ext-iconv": "*", "nette/utils": "^3.1", "php": ">=7.1 <8.2"}, "conflict": {"nette/di": "<3.0-stable"}, "require-dev": {"nette/di": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "suggest": {"ext-fileinfo": "to detect type of attached files", "ext-openssl": "to use Nette\\Mail\\DkimSigner"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📧 Nette Mail: handy email creation and transfer library for PHP with both text and MIME-compliant support.", "homepage": "https://nette.org", "keywords": ["mail", "mailer", "mime", "nette", "smtp"], "support": {"issues": "https://github.com/nette/mail/issues", "source": "https://github.com/nette/mail/tree/v3.1.8"}, "time": "2021-08-25T00:07:03+00:00"}, {"name": "nette/neon", "version": "v3.3.3", "source": {"type": "git", "url": "https://github.com/nette/neon.git", "reference": "22e384da162fab42961d48eb06c06d3ad0c11b95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/neon/zipball/22e384da162fab42961d48eb06c06d3ad0c11b95", "reference": "22e384da162fab42961d48eb06c06d3ad0c11b95", "shasum": ""}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.7"}, "bin": ["bin/neon-lint"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.3-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍸 Nette NEON: encodes and decodes NEON file format.", "homepage": "https://ne-on.org", "keywords": ["export", "import", "neon", "nette", "yaml"], "support": {"issues": "https://github.com/nette/neon/issues", "source": "https://github.com/nette/neon/tree/v3.3.3"}, "time": "2022-03-10T02:04:26+00:00"}, {"name": "nette/php-generator", "version": "v4.0.2", "source": {"type": "git", "url": "https://github.com/nette/php-generator.git", "reference": "f19b7975c7c4d729be5b64fce7eb72f0d4aac6fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/php-generator/zipball/f19b7975c7c4d729be5b64fce7eb72f0d4aac6fc", "reference": "f19b7975c7c4d729be5b64fce7eb72f0d4aac6fc", "shasum": ""}, "require": {"nette/utils": "^3.2.7 || ^4.0", "php": ">=8.0 <8.2"}, "require-dev": {"nette/tester": "^2.4", "nikic/php-parser": "^4.13", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.8"}, "suggest": {"nikic/php-parser": "to use ClassType::from(withBodies: true) & ClassType::fromCode()"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🐘 Nette PHP Generator: generates neat PHP code for you. Supports new PHP 8.1 features.", "homepage": "https://nette.org", "keywords": ["code", "nette", "php", "scaffolding"], "support": {"issues": "https://github.com/nette/php-generator/issues", "source": "https://github.com/nette/php-generator/tree/v4.0.2"}, "time": "2022-06-17T12:20:08+00:00"}, {"name": "nette/robot-loader", "version": "v3.4.1", "source": {"type": "git", "url": "https://github.com/nette/robot-loader.git", "reference": "e2adc334cb958164c050f485d99c44c430f51fe2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/robot-loader/zipball/e2adc334cb958164c050f485d99c44c430f51fe2", "reference": "e2adc334cb958164c050f485d99c44c430f51fe2", "shasum": ""}, "require": {"ext-tokenizer": "*", "nette/finder": "^2.5 || ^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🍀 Nette RobotLoader: high performance and comfortable autoloader that will search and autoload classes within your application.", "homepage": "https://nette.org", "keywords": ["autoload", "class", "interface", "nette", "trait"], "support": {"issues": "https://github.com/nette/robot-loader/issues", "source": "https://github.com/nette/robot-loader/tree/v3.4.1"}, "time": "2021-08-25T15:53:54+00:00"}, {"name": "nette/routing", "version": "v3.0.3", "source": {"type": "git", "url": "https://github.com/nette/routing.git", "reference": "5e02bdde257029db0223d3291c281d913abd587f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/routing/zipball/5e02bdde257029db0223d3291c281d913abd587f", "reference": "5e02bdde257029db0223d3291c281d913abd587f", "shasum": ""}, "require": {"nette/http": "^3.0", "nette/utils": "^3.0", "php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^1", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Routing: two-ways URL conversion", "homepage": "https://nette.org", "keywords": ["nette"], "support": {"issues": "https://github.com/nette/routing/issues", "source": "https://github.com/nette/routing/tree/v3.0.3"}, "time": "2022-04-16T23:08:16+00:00"}, {"name": "nette/safe-stream", "version": "v2.5.0", "source": {"type": "git", "url": "https://github.com/nette/safe-stream.git", "reference": "8bbbeda8415b8352642d7566dfa18169d40c2e54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/safe-stream/zipball/8bbbeda8415b8352642d7566dfa18169d40c2e54", "reference": "8bbbeda8415b8352642d7566dfa18169d40c2e54", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.5-dev"}}, "autoload": {"files": ["src/loader.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette SafeStream: provides isolation for thread safe manipulation with files via native PHP functions.", "homepage": "https://nette.org", "keywords": ["atomic", "filesystem", "isolation", "nette", "safe", "thread safe"], "support": {"issues": "https://github.com/nette/safe-stream/issues", "source": "https://github.com/nette/safe-stream/tree/v2.5.0"}, "time": "2022-01-03T23:13:32+00:00"}, {"name": "nette/schema", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/nette/schema.git", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/schema/zipball/9a39cef03a5b34c7de64f551538cbba05c2be5df", "reference": "9a39cef03a5b34c7de64f551538cbba05c2be5df", "shasum": ""}, "require": {"nette/utils": "^2.5.7 || ^3.1.5 ||  ^4.0", "php": ">=7.1 <8.2"}, "require-dev": {"nette/tester": "^2.3 || ^2.4", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "📐 Nette Schema: validating data structures against a given Schema.", "homepage": "https://nette.org", "keywords": ["config", "nette"], "support": {"issues": "https://github.com/nette/schema/issues", "source": "https://github.com/nette/schema/tree/v1.2.2"}, "time": "2021-10-15T11:40:02+00:00"}, {"name": "nette/security", "version": "v3.1.5", "source": {"type": "git", "url": "https://github.com/nette/security.git", "reference": "c120893f561b09494486c66594720b2abcb099b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/security/zipball/c120893f561b09494486c66594720b2abcb099b2", "reference": "c120893f561b09494486c66594720b2abcb099b2", "shasum": ""}, "require": {"nette/utils": "^3.2.1", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0-stable", "nette/http": "<3.1.3"}, "require-dev": {"nette/di": "^3.0.1", "nette/http": "^3.0.0", "nette/tester": "^2.0", "phpstan/phpstan-nette": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🔑 Nette Security: provides authentication, authorization and a role-based access control management via ACL (Access Control List)", "homepage": "https://nette.org", "keywords": ["Authentication", "acl", "authorization", "nette"], "support": {"issues": "https://github.com/nette/security/issues", "source": "https://github.com/nette/security/tree/v3.1.5"}, "time": "2021-09-20T15:20:25+00:00"}, {"name": "nette/tokenizer", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/nette/tokenizer.git", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tokenizer/zipball/370c5e4e2e10eb4d3e406d3a90526f821de98190", "reference": "370c5e4e2e10eb4d3e406d3a90526f821de98190", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "<PERSON><PERSON>", "homepage": "https://nette.org", "support": {"source": "https://github.com/nette/tokenizer/tree/v3.1.1"}, "abandoned": true, "time": "2022-02-09T22:28:54+00:00"}, {"name": "nette/utils", "version": "v3.2.7", "source": {"type": "git", "url": "https://github.com/nette/utils.git", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/utils/zipball/0af4e3de4df9f1543534beab255ccf459e7a2c99", "reference": "0af4e3de4df9f1543534beab255ccf459e7a2c99", "shasum": ""}, "require": {"php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0.6"}, "require-dev": {"nette/tester": "~2.0", "phpstan/phpstan": "^1.0", "tracy/tracy": "^2.3"}, "suggest": {"ext-gd": "to use Image", "ext-iconv": "to use Strings::webalize(), to<PERSON>cii(), chr() and reverse()", "ext-intl": "to use Strings::webalize(), toAscii(), normalize() and compare()", "ext-json": "to use Nette\\Utils\\Json", "ext-mbstring": "to use Strings::lower() etc...", "ext-tokenizer": "to use Nette\\Utils\\Reflection::getUseStatements()", "ext-xml": "to use Strings::length() etc. when mbstring is not available"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "🛠  Nette Utils: lightweight utilities for string & array manipulation, image handling, safe JSON encoding/decoding, validation, slug or strong password generating etc.", "homepage": "https://nette.org", "keywords": ["array", "core", "datetime", "images", "json", "nette", "paginator", "password", "slugify", "string", "unicode", "utf-8", "utility", "validation"], "support": {"issues": "https://github.com/nette/utils/issues", "source": "https://github.com/nette/utils/tree/v3.2.7"}, "time": "2022-01-24T11:29:14+00:00"}, {"name": "nettrine/annotations", "version": "v0.7.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-annotations.git", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-annotations/zipball/fbb06d156a4edcbf37e4154e5b4ede079136388b", "reference": "fbb06d156a4edcbf37e4154e5b4ede079136388b", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/annotations": "^1.6.1", "nettrine/cache": "^0.3.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.10.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.8.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Annotations\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Annotations for Nette Framework", "homepage": "https://github.com/nettrine/annotations", "keywords": ["annotations", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-annotations/issues", "source": "https://github.com/contributte/doctrine-annotations/tree/v0.7.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T18:01:50+00:00"}, {"name": "nettrine/cache", "version": "v0.3.0", "source": {"type": "git", "url": "https://github.com/contributte/doctrine-cache.git", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/doctrine-cache/zipball/8a58596de24cdd61e45866ef8f35788675f6d2bc", "reference": "8a58596de24cdd61e45866ef8f35788675f6d2bc", "shasum": ""}, "require": {"contributte/di": "^0.5.0", "doctrine/cache": "^1.8.0", "php": ">=7.2"}, "require-dev": {"ninjify/qa": "^0.9.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan-deprecation-rules": "^0.11.0", "phpstan/phpstan-nette": "^0.11.0", "phpstan/phpstan-shim": "^0.11.5", "phpstan/phpstan-strict-rules": "^0.11.0", "phpunit/phpunit": "^8.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.3.x-dev"}}, "autoload": {"psr-4": {"Nettrine\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MPL-2.0"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://github.com/mabar"}], "description": "Doctrine Cache for Nette Framework", "homepage": "https://github.com/nettrine/cache", "keywords": ["cache", "doctrine", "nette", "nettrine"], "support": {"issues": "https://github.com/contributte/doctrine-cache/issues", "source": "https://github.com/contributte/doctrine-cache/tree/v0.3.0"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2020-12-10T17:56:32+00:00"}, {"name": "nextras/dbal", "version": "v4.0.4", "source": {"type": "git", "url": "https://github.com/nextras/dbal.git", "reference": "05c44e728bd9362699c7a0b65ed458fcb62d02ac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/dbal/zipball/05c44e728bd9362699c7a0b65ed458fcb62d02ac", "reference": "05c44e728bd9362699c7a0b65ed458fcb62d02ac", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"mockery/mockery": "~1.3.0", "nette/caching": "~3.0", "nette/di": "~3.0", "nette/finder": "~2.5", "nette/neon": "~3.0", "nette/tester": "~2.3.1", "nette/utils": "~3.0", "phpstan/extension-installer": "1.0.5", "phpstan/phpstan": "1.0.0", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-strict-rules": "1.0.0", "symfony/config": "~4.4 || ~5.0", "symfony/dependency-injection": "~4.4 || ~5.0", "symfony/http-kernel": "~4.4 || ~5.0", "tracy/tracy": "~2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"files": ["srcCompat/compatibility.php"], "psr-4": {"Nextras\\Dbal\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/dbal/graphs/contributors"}], "description": "Nextras database abstraction layer", "homepage": "https://github.com/nextras/dbal", "keywords": ["database", "dbal", "nextras"], "support": {"issues": "https://github.com/nextras/dbal/issues", "source": "https://github.com/nextras/dbal/tree/v4.0.4"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2022-01-09T20:39:43+00:00"}, {"name": "nextras/migrations", "version": "v3.1.4", "source": {"type": "git", "url": "https://github.com/nextras/migrations.git", "reference": "1eaebea155d61752acff1a3ee6ad8ebcf535ff7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/migrations/zipball/1eaebea155d61752acff1a3ee6ad8ebcf535ff7f", "reference": "1eaebea155d61752acff1a3ee6ad8ebcf535ff7f", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"dibi/dibi": "~3.0 | ~4.0", "doctrine/cache": "~1.5", "doctrine/dbal": "~2.5", "doctrine/orm": "~2.5", "ext-openssl": "*", "mockery/mockery": "~0.9 | ~1.0", "nette/database": "~2.2", "nette/di": "~2.3.12 | ~2.4", "nette/tester": "~1.7 | ~2.0", "nette/utils": "~2.3", "nextras/dbal": "~1.0 | ~2.0 | ~3.0 | ~4.0", "symfony/config": "~2.6 | ~3.0 | ~4.0", "symfony/console": "~2.6 | ~3.0 | ~4.0", "symfony/dependency-injection": "~2.6 | ~3.0 | ~4.0", "symfony/http-kernel": "~2.6 | ~3.0 | ~4.0", "tracy/tracy": "^2.2"}, "suggest": {"dibi/dibi": "to use DibiAdapter", "doctrine/dbal": "to use DoctrineAdapter", "doctrine/orm": "to generate migrations with Doctrine SchemaTool", "nette/database": "to use NetteAdapter", "nextras/dbal": "to use NextrasAdapter", "symfony/console": "to use Symfony commands"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Nextras\\Migrations\\": "src/"}, "classmap": ["src/exceptions.php", "src/deprecated"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Database migrations runner", "support": {"issues": "https://github.com/nextras/migrations/issues", "source": "https://github.com/nextras/migrations/tree/v3.1.4"}, "time": "2021-12-06T19:54:05+00:00"}, {"name": "nextras/orm", "version": "v4.0.4", "source": {"type": "git", "url": "https://github.com/nextras/orm.git", "reference": "2c4058878b8241a215bcbcdb5953604f5dc7f057"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm/zipball/2c4058878b8241a215bcbcdb5953604f5dc7f057", "reference": "2c4058878b8241a215bcbcdb5953604f5dc7f057", "shasum": ""}, "require": {"ext-ctype": "*", "ext-json": "*", "nette/caching": "~2.5 || ~3.0", "nette/tokenizer": "~2.3 || ~3.0", "nette/utils": "~2.5 || ~3.0", "nextras/dbal": "~4.0@dev", "php": ">=7.1"}, "require-dev": {"marc-mabe/php-enum": "~3.0", "marc-mabe/php-enum-phpstan": "2.0.0", "mockery/mockery": "~1.2", "nette/bootstrap": "~2.4 || ~3.0", "nette/di": "~2.4 >=2.4.10 || ~3.0", "nette/finder": "~2.4 || ~3.0", "nette/neon": "~2.4 || ~3.0", "nette/tester": "~2.3", "nextras/orm-phpstan": "1.0.0", "phpstan/extension-installer": "1.0.5", "phpstan/phpstan": "1.1.2", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-mockery": "1.0.0", "phpstan/phpstan-nette": "1.0.0", "phpstan/phpstan-strict-rules": "1.0.0", "tracy/tracy": "~2.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0-dev"}}, "autoload": {"files": ["srcCompat/compatibility.php"], "psr-4": {"Nextras\\Orm\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Nextras Project", "homepage": "https://github.com/nextras/orm/graphs/contributors"}], "description": "Nextras Orm framework", "homepage": "https://github.com/nextras/orm", "keywords": ["database", "nextras", "orm"], "support": {"issues": "https://github.com/nextras/orm/issues", "source": "https://github.com/nextras/orm/tree/v4.0.4"}, "funding": [{"url": "https://github.com/hrach", "type": "github"}], "time": "2021-11-13T15:57:41+00:00"}, {"name": "nyholm/dsn", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/Nyholm/dsn.git", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Nyholm/dsn/zipball/9445621b426bac8c0ca161db8cd700da00a4e618", "reference": "9445621b426bac8c0ca161db8cd700da00a4e618", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"symfony/phpunit-bridge": "^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"psr-4": {"Nyholm\\Dsn\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Parse your DSN strings in a powerful and flexible way", "homepage": "http://tnyholm.se", "keywords": ["database", "dsn", "dsn parser", "parser"], "support": {"issues": "https://github.com/Nyholm/dsn/issues", "source": "https://github.com/Nyholm/dsn/tree/2.0.1"}, "funding": [{"url": "https://github.com/Nyholm", "type": "github"}], "time": "2021-11-18T09:23:29+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2020-10-15T08:29:30+00:00"}, {"name": "pelago/emogrifier", "version": "v6.0.0", "source": {"type": "git", "url": "https://github.com/MyIntervals/emogrifier.git", "reference": "aa72d5407efac118f3896bcb995a2cba793df0ae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MyIntervals/emogrifier/zipball/aa72d5407efac118f3896bcb995a2cba793df0ae", "reference": "aa72d5407efac118f3896bcb995a2cba793df0ae", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "~7.2.0 || ~7.3.0 || ~7.4.0 || ~8.0.0 || ~8.1.0", "sabberworm/php-css-parser": "^8.3.1", "symfony/css-selector": "^3.4.32 || ^4.4 || ^5.3 || ^6.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.3.0", "phpunit/phpunit": "^8.5.16", "rawr/cross-data-providers": "^2.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "7.0.x-dev"}}, "autoload": {"psr-4": {"Pelago\\Emogrifier\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "Converts CSS styles into inline style attributes in your HTML code", "homepage": "https://www.myintervals.com/emogrifier.php", "keywords": ["css", "email", "pre-processing"], "support": {"issues": "https://github.com/MyIntervals/emogrifier/issues", "source": "https://github.com/MyIntervals/emogrifier"}, "time": "2021-09-16T16:22:04+00:00"}, {"name": "php-curl-class/php-curl-class", "version": "9.6.0", "source": {"type": "git", "url": "https://github.com/php-curl-class/php-curl-class.git", "reference": "9f02d09f0e449009395e3b578db420070255a458"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-curl-class/php-curl-class/zipball/9f02d09f0e449009395e3b578db420070255a458", "reference": "9f02d09f0e449009395e3b578db420070255a458", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.0"}, "require-dev": {"ext-gd": "*", "phpcompatibility/php-compatibility": "*", "phpunit/phpunit": "*", "squizlabs/php_codesniffer": "*", "vimeo/psalm": "*"}, "suggest": {"ext-mbstring": "*"}, "type": "library", "autoload": {"psr-4": {"Curl\\": "src/Curl/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Unlicense"], "authors": [{"name": "<PERSON>"}], "description": "PHP Curl Class makes it easy to send HTTP requests and integrate with web APIs.", "homepage": "https://github.com/php-curl-class/php-curl-class", "keywords": ["API-Client", "api", "class", "client", "curl", "framework", "http", "http-client", "http-proxy", "json", "php", "php-curl", "php-curl-library", "proxy", "requests", "restful", "web-scraper", "web-scraping ", "web-service", "xml"], "support": {"issues": "https://github.com/php-curl-class/php-curl-class/issues", "source": "https://github.com/php-curl-class/php-curl-class/tree/9.6.0"}, "time": "2022-03-17T15:17:56+00:00"}, {"name": "php-http/client-common", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/php-http/client-common.git", "reference": "d135751167d57e27c74de674d6a30cef2dc8e054"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/client-common/zipball/d135751167d57e27c74de674d6a30cef2dc8e054", "reference": "d135751167d57e27c74de674d6a30cef2dc8e054", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/httplug": "^2.0", "php-http/message": "^1.6", "php-http/message-factory": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "symfony/options-resolver": "~4.0.15 || ~4.1.9 || ^4.2.1 || ^5.0 || ^6.0", "symfony/polyfill-php80": "^1.17"}, "require-dev": {"doctrine/instantiator": "^1.1", "guzzlehttp/psr7": "^1.4", "nyholm/psr7": "^1.2", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "phpspec/prophecy": "^1.10.2", "phpunit/phpunit": "^7.5.15 || ^8.5 || ^9.3"}, "suggest": {"ext-json": "To detect JSON responses with the ContentTypePlugin", "ext-libxml": "To detect XML responses with the ContentTypePlugin", "php-http/cache-plugin": "PSR-6 Cache plugin", "php-http/logger-plugin": "PSR-3 Logger plugin", "php-http/stopwatch-plugin": "Symfony Stopwatch plugin"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\Common\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common HTTP Client implementations and tools for HTTPlug", "homepage": "http://httplug.io", "keywords": ["client", "common", "http", "httplug"], "support": {"issues": "https://github.com/php-http/client-common/issues", "source": "https://github.com/php-http/client-common/tree/2.5.0"}, "time": "2021-11-26T15:01:24+00:00"}, {"name": "php-http/discovery", "version": "1.14.1", "source": {"type": "git", "url": "https://github.com/php-http/discovery.git", "reference": "de90ab2b41d7d61609f504e031339776bc8c7223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/discovery/zipball/de90ab2b41d7d61609f504e031339776bc8c7223", "reference": "de90ab2b41d7d61609f504e031339776bc8c7223", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"nyholm/psr7": "<1.0"}, "require-dev": {"graham-campbell/phpspec-skip-example-extension": "^5.0", "php-http/httplug": "^1.0 || ^2.0", "php-http/message-factory": "^1.0", "phpspec/phpspec": "^5.1 || ^6.1", "puli/composer-plugin": "1.0.0-beta10"}, "suggest": {"php-http/message": "Allow to use Guzzle, Diactoros or Slim Framework factories"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Http\\Discovery\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Finds installed HTTPlug implementations and PSR-7 message factories", "homepage": "http://php-http.org", "keywords": ["adapter", "client", "discovery", "factory", "http", "message", "psr7"], "support": {"issues": "https://github.com/php-http/discovery/issues", "source": "https://github.com/php-http/discovery/tree/1.14.1"}, "time": "2021-09-18T07:57:46+00:00"}, {"name": "php-http/httplug", "version": "2.3.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "f640739f80dfa1152533976e3c112477f69274eb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/f640739f80dfa1152533976e3c112477f69274eb", "reference": "f640739f80dfa1152533976e3c112477f69274eb", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1", "phpspec/phpspec": "^5.1 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.3.0"}, "time": "2022-02-21T09:52:22+00:00"}, {"name": "php-http/message", "version": "1.13.0", "source": {"type": "git", "url": "https://github.com/php-http/message.git", "reference": "7886e647a30a966a1a8d1dad1845b71ca8678361"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message/zipball/7886e647a30a966a1a8d1dad1845b71ca8678361", "reference": "7886e647a30a966a1a8d1dad1845b71ca8678361", "shasum": ""}, "require": {"clue/stream-filter": "^1.5", "php": "^7.1 || ^8.0", "php-http/message-factory": "^1.0.2", "psr/http-message": "^1.0"}, "provide": {"php-http/message-factory-implementation": "1.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.6", "ext-zlib": "*", "guzzlehttp/psr7": "^1.0", "laminas/laminas-diactoros": "^2.0", "phpspec/phpspec": "^5.1 || ^6.3 || ^7.1", "slim/slim": "^3.0"}, "suggest": {"ext-zlib": "Used with compressor/decompressor streams", "guzzlehttp/psr7": "Used with Guzzle PSR-7 Factories", "laminas/laminas-diactoros": "Used with Diactoros Factories", "slim/slim": "Used with Slim Framework PSR-7 implementation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"files": ["src/filters.php"], "psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "HTTP Message related tools", "homepage": "http://php-http.org", "keywords": ["http", "message", "psr-7"], "support": {"issues": "https://github.com/php-http/message/issues", "source": "https://github.com/php-http/message/tree/1.13.0"}, "time": "2022-02-11T13:41:14+00:00"}, {"name": "php-http/message-factory", "version": "v1.0.2", "source": {"type": "git", "url": "https://github.com/php-http/message-factory.git", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/message-factory/zipball/a478cb11f66a6ac48d8954216cfed9aa06a501a1", "reference": "a478cb11f66a6ac48d8954216cfed9aa06a501a1", "shasum": ""}, "require": {"php": ">=5.4", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Factory interfaces for PSR-7 HTTP Message", "homepage": "http://php-http.org", "keywords": ["factory", "http", "message", "stream", "uri"], "support": {"issues": "https://github.com/php-http/message-factory/issues", "source": "https://github.com/php-http/message-factory/tree/master"}, "time": "2015-12-19T14:08:53+00:00"}, {"name": "php-http/promise", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "reference": "4c4c1f9b7289a2ec57cde7f1e9762a5789506f88", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2", "phpspec/phpspec": "^5.1.2 || ^6.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.1.0"}, "time": "2020-07-07T09:29:14+00:00"}, {"name": "predis/predis", "version": "v2.0.3", "source": {"type": "git", "url": "https://github.com/predis/predis.git", "reference": "ff59f745815150c65ed388f7d64e7660fe961771"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/predis/predis/zipball/ff59f745815150c65ed388f7d64e7660fe961771", "reference": "ff59f745815150c65ed388f7d64e7660fe961771", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0 || ~9.4.4"}, "suggest": {"ext-curl": "Allows access to Webdis when paired with phpiredis", "ext-phpiredis": "Allows faster serialization and deserialization of the Redis protocol"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.0-dev"}}, "autoload": {"psr-4": {"Predis\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://clorophilla.net", "role": "Creator & Maintainer"}, {"name": "<PERSON>", "homepage": "https://till.im", "role": "Maintainer"}], "description": "A flexible and feature-complete Redis client for PHP.", "homepage": "http://github.com/predis/predis", "keywords": ["nosql", "predis", "redis"], "support": {"issues": "https://github.com/predis/predis/issues", "source": "https://github.com/predis/predis/tree/v2.0.3"}, "funding": [{"url": "https://github.com/sponsors/tillkruss", "type": "github"}], "time": "2022-10-11T16:52:29+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/container", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/513e0666f7216c7459170d56df27dfcefe1689ea", "reference": "513e0666f7216c7459170d56df27dfcefe1689ea", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/1.1.2"}, "time": "2021-11-05T16:50:12+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/ef29f6d262798707a9edd554e2b82517ef3a9376", "reference": "ef29f6d262798707a9edd554e2b82517ef3a9376", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/2.0.0"}, "time": "2021-07-14T16:41:46+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "v2.9.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/234f8fd1023c9158e2314fa9d7d0e6a83db42910", "reference": "234f8fd1023c9158e2314fa9d7d0e6a83db42910", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.9.0"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-02-11T10:27:51+00:00"}, {"name": "ruflin/elastica", "version": "7.1.5", "source": {"type": "git", "url": "https://github.com/ruflin/Elastica.git", "reference": "72a4598544e3f99b5dd8cacb05d009ee75c2a701"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ruflin/Elastica/zipball/72a4598544e3f99b5dd8cacb05d009ee75c2a701", "reference": "72a4598544e3f99b5dd8cacb05d009ee75c2a701", "shasum": ""}, "require": {"elasticsearch/elasticsearch": "^7.1.1", "ext-json": "*", "nyholm/dsn": "^2.0.0", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0", "symfony/deprecation-contracts": "^2.2 || ^3.0", "symfony/polyfill-php73": "^1.19"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "guzzlehttp/guzzle": "^6.3 || ^7.2", "phpunit/phpunit": "^8.5.8 || ^9.4", "symfony/phpunit-bridge": "^5.1.1"}, "suggest": {"aws/aws-sdk-php": "Allow using IAM authentication with Amazon ElasticSearch Service", "egeloen/http-adapter": "Allow using httpadapter as transport", "guzzlehttp/guzzle": "Allow using guzzle as transport", "monolog/monolog": "Logging request"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.0.x-dev"}}, "autoload": {"psr-4": {"Elastica\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://ruflin.com/"}], "description": "Elasticsearch Client", "homepage": "http://elastica.io/", "keywords": ["client", "search"], "support": {"issues": "https://github.com/ruflin/Elastica/issues", "source": "https://github.com/ruflin/Elastica/tree/7.1.5"}, "time": "2022-03-29T15:37:28+00:00"}, {"name": "sabberworm/php-css-parser", "version": "8.4.0", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/e41d2140031d533348b2192a83f02d8dd8a71d30", "reference": "e41d2140031d533348b2192a83f02d8dd8a71d30", "shasum": ""}, "require": {"ext-iconv": "*", "php": ">=5.6.20"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "^4.8.36"}, "suggest": {"ext-mbstring": "for parsing UTF-8 CSS"}, "type": "library", "autoload": {"psr-4": {"Sabberworm\\CSS\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "https://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.4.0"}, "time": "2021-12-11T13:40:54+00:00"}, {"name": "sentry/sdk", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php-sdk.git", "reference": "2de7de3233293f80d1e244bd950adb2121a3731c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php-sdk/zipball/2de7de3233293f80d1e244bd950adb2121a3731c", "reference": "2de7de3233293f80d1e244bd950adb2121a3731c", "shasum": ""}, "require": {"http-interop/http-factory-guzzle": "^1.0", "sentry/sentry": "^3.1", "symfony/http-client": "^4.3|^5.0|^6.0"}, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "This is a metapackage shipping sentry/sentry with a recommended HTTP client.", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"source": "https://github.com/getsentry/sentry-php-sdk/tree/3.1.1"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2021-11-30T11:54:41+00:00"}, {"name": "sentry/sentry", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "a92443883df6a55cbe7a062f76949f23dda66772"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/a92443883df6a55cbe7a062f76949f23dda66772", "reference": "a92443883df6a55cbe7a062f76949f23dda66772", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "guzzlehttp/promises": "^1.4", "guzzlehttp/psr7": "^1.7|^2.0", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "php-http/async-client-implementation": "^1.0", "php-http/client-common": "^1.5|^2.0", "php-http/discovery": "^1.11", "php-http/httplug": "^1.1|^2.0", "php-http/message": "^1.5", "psr/http-factory": "^1.0", "psr/http-message-implementation": "^1.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^3.4.43|^4.4.30|^5.0.11|^6.0", "symfony/polyfill-php80": "^1.17", "symfony/polyfill-uuid": "^1.13.1"}, "conflict": {"php-http/client-common": "1.8.0", "raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.19|3.4.*", "http-interop/http-factory-guzzle": "^1.0", "monolog/monolog": "^1.3|^2.0", "nikic/php-parser": "^4.10.3", "php-http/mock-client": "^1.3", "phpbench/phpbench": "^1.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.3", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.4.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "A PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "sentry"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/3.4.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2022-03-13T12:38:01+00:00"}, {"name": "setasign/fpdi", "version": "v2.3.6", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2021-02-11T11:37:01+00:00"}, {"name": "symfony/amqp-messenger", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/amqp-messenger.git", "reference": "4065cb4af96eb9ade8d33e38f9ce99de7d42f090"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/amqp-messenger/zipball/4065cb4af96eb9ade8d33e38f9ce99de7d42f090", "reference": "4065cb4af96eb9ade8d33e38f9ce99de7d42f090", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.3|^6.0"}, "require-dev": {"symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Amqp\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony AMQP extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/amqp-messenger/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T16:58:25+00:00"}, {"name": "symfony/cache", "version": "v6.0.6", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "57faad4e0d694f9961f517fdd5e6fbb1f6d0e04f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/57faad4e0d694f9961f517fdd5e6fbb1f6d0e04f", "reference": "57faad4e0d694f9961f517fdd5e6fbb1f6d0e04f", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^1.1.7|^2|^3", "symfony/service-contracts": "^1.1|^2|^3", "symfony/var-exporter": "^5.4|^6.0"}, "conflict": {"doctrine/dbal": "<2.13.1", "symfony/dependency-injection": "<5.4", "symfony/http-kernel": "<5.4", "symfony/var-dumper": "<5.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^2.13.1|^3.0", "predis/predis": "^1.1", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/filesystem": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/messenger": "^5.4|^6.0", "symfony/var-dumper": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an extended PSR-6, PSR-16 (and tags) implementation", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v6.0.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-02T12:58:14+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "2eab7fa459af6d75c6463e63e633b667a9b761d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/2eab7fa459af6d75c6463e63e633b667a9b761d3", "reference": "2eab7fa459af6d75c6463e63e633b667a9b761d3", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "suggest": {"symfony/cache-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T11:15:52+00:00"}, {"name": "symfony/config", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "22850bfdd2b6090568ad05dece6843c859d933b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/22850bfdd2b6090568ad05dece6843c859d933b7", "reference": "22850bfdd2b6090568ad05dece6843c859d933b7", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.1|^3", "symfony/filesystem": "^5.4|^6.0", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-php81": "^1.22"}, "conflict": {"symfony/finder": "<4.4"}, "require-dev": {"symfony/event-dispatcher": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/messenger": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"symfony/yaml": "To use the yaml reference dumper"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-22T16:12:04+00:00"}, {"name": "symfony/console", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/535846c7ee6bc4dd027ca0d93220601456734b10", "reference": "535846c7ee6bc4dd027ca0d93220601456734b10", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.16", "symfony/service-contracts": "^1.1|^2|^3", "symfony/string": "^5.1|^6.0"}, "conflict": {"psr/log": ">=3", "symfony/dependency-injection": "<4.4", "symfony/dotenv": "<5.1", "symfony/event-dispatcher": "<4.4", "symfony/lock": "<4.4", "symfony/process": "<4.4"}, "provide": {"psr/log-implementation": "1.0|2.0"}, "require-dev": {"psr/log": "^1|^2", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/lock": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/var-dumper": "^4.4|^5.0|^6.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-22T10:42:43+00:00"}, {"name": "symfony/css-selector", "version": "v6.0.3", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "1955d595c12c111629cc814d3f2a2ff13580508a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/1955d595c12c111629cc814d3f2a2ff13580508a", "reference": "1955d595c12c111629cc814d3f2a2ff13580508a", "shasum": ""}, "require": {"php": ">=8.0.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v6.0.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918", "reference": "07f1b9cc2ffee6aaafcf4b710fbc38ff736bd918", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T11:15:52+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v6.1.3", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "9a2e1b98ac5a13f48caacb1a8c9f3e9df24bd402"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/9a2e1b98ac5a13f48caacb1a8c9f3e9df24bd402", "reference": "9a2e1b98ac5a13f48caacb1a8c9f3e9df24bd402", "shasum": ""}, "require": {"doctrine/dbal": "^2.13|^3.0", "php": ">=8.1", "symfony/messenger": "^5.4|^6.0", "symfony/service-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "symfony/property-access": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v6.1.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-28T13:40:41+00:00"}, {"name": "symfony/event-dispatcher", "version": "v5.4.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "reference": "8e6ce1cc0279e3ff3c8ff0f43813bc88d21ca1bc", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/event-dispatcher-contracts": "^2|^3", "symfony/polyfill-php80": "^1.16"}, "conflict": {"symfony/dependency-injection": "<4.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^4.4|^5.0|^6.0", "symfony/dependency-injection": "^4.4|^5.0|^6.0", "symfony/error-handler": "^4.4|^5.0|^6.0", "symfony/expression-language": "^4.4|^5.0|^6.0", "symfony/http-foundation": "^4.4|^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v5.4.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-05T16:45:39+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "02ff5eea2f453731cfbc6bc215e456b781480448"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/02ff5eea2f453731cfbc6bc215e456b781480448", "reference": "02ff5eea2f453731cfbc6bc215e456b781480448", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "suggest": {"symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-02-25T11:15:52+00:00"}, {"name": "symfony/filesystem", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "6c9e4c41f2c51dfde3db298594ed9cba55dbf5ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/6c9e4c41f2c51dfde3db298594ed9cba55dbf5ff", "reference": "6c9e4c41f2c51dfde3db298594ed9cba55dbf5ff", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-01T12:54:51+00:00"}, {"name": "symfony/http-client", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/http-client.git", "reference": "a7930c47248b9b57e9d0b8da100ffc1e031536dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client/zipball/a7930c47248b9b57e9d0b8da100ffc1e031536dc", "reference": "a7930c47248b9b57e9d0b8da100ffc1e031536dc", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3", "symfony/http-client-contracts": "^3", "symfony/service-contracts": "^1.0|^2|^3"}, "provide": {"php-http/async-client-implementation": "*", "php-http/client-implementation": "*", "psr/http-client-implementation": "1.0", "symfony/http-client-implementation": "3.0"}, "require-dev": {"amphp/amp": "^2.5", "amphp/http-client": "^4.2.1", "amphp/http-tunnel": "^1.0", "amphp/socket": "^1.1", "guzzlehttp/promises": "^1.4", "nyholm/psr7": "^1.0", "php-http/httplug": "^1.0|^2.0", "psr/http-client": "^1.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/process": "^5.4|^6.0", "symfony/stopwatch": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpClient\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides powerful methods to fetch HTTP resources synchronously or asynchronously", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-client/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-01T12:27:43+00:00"}, {"name": "symfony/http-client-contracts", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "fd038f08c623ab5d22b26e9ba35afe8c79071800"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/fd038f08c623ab5d22b26e9ba35afe8c79071800", "reference": "fd038f08c623ab5d22b26e9ba35afe8c79071800", "shasum": ""}, "require": {"php": ">=8.1"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v3.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-04-22T07:30:54+00:00"}, {"name": "symfony/lock", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/lock.git", "reference": "dcbb2a00ddf90f0ea8d370c354e0cde1303bf4a5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/lock/zipball/dcbb2a00ddf90f0ea8d370c354e0cde1303bf4a5", "reference": "dcbb2a00ddf90f0ea8d370c354e0cde1303bf4a5", "shasum": ""}, "require": {"php": ">=8.0.2", "psr/log": "^1|^2|^3"}, "conflict": {"doctrine/dbal": "<2.13"}, "require-dev": {"doctrine/dbal": "^2.13|^3.0", "predis/predis": "~1.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Lock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Creates and manages locks, a mechanism to provide exclusive access to a shared resource", "homepage": "https://symfony.com", "keywords": ["cas", "flock", "locking", "mutex", "redlock", "semaphore"], "support": {"source": "https://github.com/symfony/lock/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-22T16:12:04+00:00"}, {"name": "symfony/messenger", "version": "v5.4.11", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "05cebeb1f3dcefd33eb5275709e9ff7cc0df50fd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/05cebeb1f3dcefd33eb5275709e9ff7cc0df50fd", "reference": "05cebeb1f3dcefd33eb5275709e9ff7cc0df50fd", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/log": "^1|^2|^3", "symfony/amqp-messenger": "^5.1|^6.0", "symfony/deprecation-contracts": "^2.1|^3", "symfony/doctrine-messenger": "^5.1|^6.0", "symfony/polyfill-php80": "^1.16", "symfony/redis-messenger": "^5.1|^6.0"}, "conflict": {"symfony/event-dispatcher": "<4.4", "symfony/framework-bundle": "<4.4", "symfony/http-kernel": "<4.4", "symfony/serializer": "<5.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.3|^6.0", "symfony/event-dispatcher": "^4.4|^5.0|^6.0", "symfony/http-kernel": "^4.4|^5.0|^6.0", "symfony/process": "^4.4|^5.0|^6.0", "symfony/property-access": "^4.4|^5.0|^6.0", "symfony/routing": "^4.4|^5.0|^6.0", "symfony/serializer": "^5.0|^6.0", "symfony/service-contracts": "^1.1|^2|^3", "symfony/stopwatch": "^4.4|^5.0|^6.0", "symfony/validator": "^4.4|^5.0|^6.0"}, "suggest": {"enqueue/messenger-adapter": "For using the php-enqueue library as a transport."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v5.4.11"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-20T13:00:38+00:00"}, {"name": "symfony/options-resolver", "version": "v6.0.3", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "51f7006670febe4cbcbae177cbffe93ff833250d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/51f7006670febe4cbcbae177cbffe93ff833250d", "reference": "51f7006670febe4cbcbae177cbffe93ff833250d", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/deprecation-contracts": "^2.1|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v6.0.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:55:41+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "reference": "6fd1b9a79f6e3cf65f9e679b23af304cd9e010d4", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "433d05519ce6990bf3530fba6957499d327395c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/433d05519ce6990bf3530fba6957499d327395c2", "reference": "433d05519ce6990bf3530fba6957499d327395c2", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "219aa369ceff116e673852dce47c3a41794c14bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/219aa369ceff116e673852dce47c3a41794c14bd", "reference": "219aa369ceff116e673852dce47c3a41794c14bd", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "reference": "9344f9cb97f3b19424af1a21a3b0e75b0a7d8d7e", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/e440d35fa0286f77fb45b79a03fedbeda9307e85", "reference": "e440d35fa0286f77fb45b79a03fedbeda9307e85", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-24T11:49:31+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.26.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/cfa0ae98841b9e461207c13ab093d76b0fa7bace", "reference": "cfa0ae98841b9e461207c13ab093d76b0fa7bace", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.26-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.26.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-10T07:21:04+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "reference": "5de4ba2d41b15f9bd0e19b2ab9674135813ec98f", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-09-13T13:58:11+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.25.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "7529922412d23ac44413d0f308861d50cf68d3ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/7529922412d23ac44413d0f308861d50cf68d3ee", "reference": "7529922412d23ac44413d0f308861d50cf68d3ee", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.23-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.25.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-10-20T20:35:02+00:00"}, {"name": "symfony/property-access", "version": "v6.2.5", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "cfd63e46c8b8a97f05353fb9341bfa75a62184e1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/cfd63e46c8b8a97f05353fb9341bfa75a62184e1", "reference": "cfd63e46c8b8a97f05353fb9341bfa75a62184e1", "shasum": ""}, "require": {"php": ">=8.1", "symfony/deprecation-contracts": "^2.1|^3", "symfony/property-info": "^5.4|^6.0"}, "require-dev": {"symfony/cache": "^5.4|^6.0"}, "suggest": {"psr/cache-implementation": "To cache access methods."}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v6.2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-01-01T08:38:09+00:00"}, {"name": "symfony/property-info", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "0f26f0870f05d65d5c06681ecbf36e546204f4b5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/0f26f0870f05d65d5c06681ecbf36e546204f4b5", "reference": "0f26f0870f05d65d5c06681ecbf36e546204f4b5", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/string": "^5.4|^6.0"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/dependency-injection": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/serializer": "^5.4|^6.0"}, "suggest": {"phpdocumentor/reflection-docblock": "To use the PHPDoc", "psr/cache-implementation": "To cache results", "symfony/doctrine-bridge": "To use Doctrine metadata", "symfony/serializer": "To use Serializer metadata"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-31T17:18:25+00:00"}, {"name": "symfony/redis-messenger", "version": "v5.4.6", "source": {"type": "git", "url": "https://github.com/symfony/redis-messenger.git", "reference": "6b14778e0a6295af023fec637c7b38f918c1a08d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/redis-messenger/zipball/6b14778e0a6295af023fec637c7b38f918c1a08d", "reference": "6b14778e0a6295af023fec637c7b38f918c1a08d", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1|^3", "symfony/messenger": "^5.1|^6.0"}, "require-dev": {"symfony/property-access": "^4.4|^5.0|^6.0", "symfony/serializer": "^4.4|^5.0|^6.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Redis\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Redis extension Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/redis-messenger/tree/v5.4.6"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-04T15:46:40+00:00"}, {"name": "symfony/service-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "reference": "4b426aac47d6427cc1a1d0f7e2ac724627f5966c", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.1", "symfony/deprecation-contracts": "^2.1|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-05-30T19:17:29+00:00"}, {"name": "symfony/string", "version": "v6.1.3", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "f35241f45c30bcd9046af2bb200a7086f70e1d6b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/f35241f45c30bcd9046af2bb200a7086f70e1d6b", "reference": "f35241f45c30bcd9046af2bb200a7086f70e1d6b", "shasum": ""}, "require": {"php": ">=8.1", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.0"}, "require-dev": {"symfony/error-handler": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/translation-contracts": "^2.0|^3.0", "symfony/var-exporter": "^5.4|^6.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v6.1.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-27T15:50:51+00:00"}, {"name": "symfony/translation-contracts", "version": "v3.1.1", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "606be0f48e05116baef052f7f3abdb345c8e02cc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/606be0f48e05116baef052f7f3abdb345c8e02cc", "reference": "606be0f48e05116baef052f7f3abdb345c8e02cc", "shasum": ""}, "require": {"php": ">=8.1"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v3.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T17:24:16+00:00"}, {"name": "symfony/validator", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/validator.git", "reference": "62f66d9c3141429f4328e0a3981da5669cabaef0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/validator/zipball/62f66d9c3141429f4328e0a3981da5669cabaef0", "reference": "62f66d9c3141429f4328e0a3981da5669cabaef0", "shasum": ""}, "require": {"php": ">=8.0.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php81": "^1.22", "symfony/translation-contracts": "^1.1|^2|^3"}, "conflict": {"doctrine/annotations": "<1.13", "doctrine/lexer": "<1.1", "phpunit/phpunit": "<5.4.3", "symfony/dependency-injection": "<5.4", "symfony/expression-language": "<5.4", "symfony/http-kernel": "<5.4", "symfony/intl": "<5.4", "symfony/property-info": "<5.4", "symfony/translation": "<5.4", "symfony/yaml": "<5.4"}, "require-dev": {"doctrine/annotations": "^1.13", "egulias/email-validator": "^2.1.10|^3", "symfony/cache": "^5.4|^6.0", "symfony/config": "^5.4|^6.0", "symfony/console": "^5.4|^6.0", "symfony/dependency-injection": "^5.4|^6.0", "symfony/expression-language": "^5.4|^6.0", "symfony/finder": "^5.4|^6.0", "symfony/http-client": "^5.4|^6.0", "symfony/http-foundation": "^5.4|^6.0", "symfony/http-kernel": "^5.4|^6.0", "symfony/intl": "^5.4|^6.0", "symfony/mime": "^5.4|^6.0", "symfony/property-access": "^5.4|^6.0", "symfony/property-info": "^5.4|^6.0", "symfony/translation": "^5.4|^6.0", "symfony/yaml": "^5.4|^6.0"}, "suggest": {"egulias/email-validator": "Strict (RFC compliant) email validation", "psr/cache-implementation": "For using the mapping cache.", "symfony/config": "", "symfony/expression-language": "For using the Expression validator and the ExpressionLanguageSyntax constraints", "symfony/http-foundation": "", "symfony/intl": "", "symfony/property-access": "For accessing properties within comparison constraints", "symfony/property-info": "To automatically add NotNull and Type constraints", "symfony/translation": "For translating validation errors.", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Validator\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to validate values", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/validator/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-31T17:18:25+00:00"}, {"name": "symfony/var-exporter", "version": "v6.0.7", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "5f1fddb1b3a8394dbfb234044e3ad620a26e1735"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/5f1fddb1b3a8394dbfb234044e3ad620a26e1735", "reference": "5f1fddb1b3a8394dbfb234044e3ad620a26e1735", "shasum": ""}, "require": {"php": ">=8.0.2"}, "require-dev": {"symfony/var-dumper": "^5.4|^6.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v6.0.7"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-03-31T17:18:25+00:00"}, {"name": "texy/texy", "version": "v3.1.5", "source": {"type": "git", "url": "https://github.com/dg/texy.git", "reference": "a8736caf7581b84a6fce3d8fb9bd713ed0fa4a64"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dg/texy/zipball/a8736caf7581b84a6fce3d8fb9bd713ed0fa4a64", "reference": "a8736caf7581b84a6fce3d8fb9bd713ed0fa4a64", "shasum": ""}, "require": {"php": ">=7.1"}, "replace": {"dg/texy": "*"}, "require-dev": {"latte/latte": "^2.6", "nette/tester": "^2.0", "phpstan/phpstan": "^0.12", "tracy/tracy": "^2.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}], "description": "Texy converts plain text in easy to read Texy syntax into structurally valid (X)HTML. It supports adding of images, links, nested lists, tables and has full support for CSS. Texy supports hyphenation of long words (which reflects language rules), clickable emails and URL (emails are obfuscated against spambots), national typographic single and double quotation marks, ellipses, em dashes, dimension sign, nonbreakable spaces (e.g. in phone numbers), acronyms, arrows and many others. Texy code can optionally contain HTML tags.", "homepage": "https://texy.info", "keywords": ["html", "markdown", "markup language", "plain text", "text", "textile", "texy", "wiki"], "support": {"source": "https://github.com/dg/texy/tree/v3.1.5"}, "time": "2021-10-28T12:28:31+00:00"}, {"name": "tracy/tracy", "version": "v2.9.4", "source": {"type": "git", "url": "https://github.com/nette/tracy.git", "reference": "0ed605329b095f5f5fe2db2adc3d1ee80c917294"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tracy/zipball/0ed605329b095f5f5fe2db2adc3d1ee80c917294", "reference": "0ed605329b095f5f5fe2db2adc3d1ee80c917294", "shasum": ""}, "require": {"ext-json": "*", "ext-session": "*", "php": ">=7.2 <8.2"}, "conflict": {"nette/di": "<3.0"}, "require-dev": {"latte/latte": "^2.5", "nette/di": "^3.0", "nette/mail": "^3.0", "nette/tester": "^2.2", "nette/utils": "^3.0", "phpstan/phpstan": "^1.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.9-dev"}}, "autoload": {"files": ["src/Tracy/functions.php"], "classmap": ["src"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "😎  Tracy: the addictive tool to ease debugging PHP code for cool developers. Friendly design, logging, profiler, advanced features like debugging AJAX calls or CLI support. You will love it.", "homepage": "https://tracy.nette.org", "keywords": ["Xdebug", "debug", "debugger", "nette", "profiler"], "support": {"issues": "https://github.com/nette/tracy/issues", "source": "https://github.com/nette/tracy/tree/v2.9.4"}, "time": "2022-07-19T14:06:15+00:00"}, {"name": "ublaboo/datagrid", "version": "v6.9.5", "source": {"type": "git", "url": "https://github.com/contributte/datagrid.git", "reference": "e7b0ceb758572422c5152abd3f7a560746e9499c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/contributte/datagrid/zipball/e7b0ceb758572422c5152abd3f7a560746e9499c", "reference": "e7b0ceb758572422c5152abd3f7a560746e9499c", "shasum": ""}, "require": {"contributte/application": "^0.5.0", "nette/di": "^3.0.0", "nette/forms": "^3.1.3", "nette/utils": "^3.0.1", "php": ">=7.2", "symfony/property-access": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0"}, "require-dev": {"contributte/code-rules": "^1.1.0", "dibi/dibi": "^3.0.0 || ^4.0.0", "doctrine/annotations": "^1.12.1", "doctrine/cache": "^1.11.0", "doctrine/orm": "^2.11.1", "elasticsearch/elasticsearch": "^7.1", "mockery/mockery": "^1.3.3", "nette/database": "^3.0.2", "nette/tester": "^2.3.4", "nextras/dbal": "^3.0.1 || ^4.0", "nextras/orm": "^3.1.0 || ^4.0", "ninjify/coding-standard": "^0.12.1", "phpstan/phpstan-nette": "^1.0.0", "tharos/leanmapper": "^3.4.2 || ^4.0.0", "tracy/tracy": "^2.6.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.10.x-dev"}}, "autoload": {"psr-4": {"Ublaboo\\DataGrid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "https://paveljanda.com"}, {"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "DataGrid for Nette Framework: filtering, sorting, pagination, tree view, table view, translator, etc", "keywords": ["contributte", "data", "datagrid", "grid", "nette", "table"], "support": {"issues": "https://github.com/contributte/datagrid/issues", "source": "https://github.com/contributte/datagrid/tree/v6.9.5"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-04-29T12:05:58+00:00"}], "packages-dev": [{"name": "dealerdirect/phpcodesniffer-composer-installer", "version": "v0.7.2", "source": {"type": "git", "url": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer.git", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Dealerdirect/phpcodesniffer-composer-installer/zipball/1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "reference": "1c968e542d8843d7cd71de3c5c9c3ff3ad71a1db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3", "squizlabs/php_codesniffer": "^2.0 || ^3.1.0 || ^4.0"}, "require-dev": {"composer/composer": "*", "php-parallel-lint/php-parallel-lint": "^1.3.1", "phpcompatibility/php-compatibility": "^9.0"}, "type": "composer-plugin", "extra": {"class": "Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\Plugin"}, "autoload": {"psr-4": {"Dealerdirect\\Composer\\Plugin\\Installers\\PHPCodeSniffer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.frenck.nl", "role": "Developer / IT Manager"}, {"name": "Contributors", "homepage": "https://github.com/Dealerdirect/phpcodesniffer-composer-installer/graphs/contributors"}], "description": "PHP_CodeSniffer Standards Composer Installer Plugin", "homepage": "http://www.dealerdirect.com", "keywords": ["PHPCodeSniffer", "PHP_CodeSniffer", "code quality", "codesniffer", "composer", "installer", "phpcbf", "phpcs", "plugin", "qa", "quality", "standard", "standards", "style guide", "stylecheck", "tests"], "support": {"issues": "https://github.com/dealerdirect/phpcodesniffer-composer-installer/issues", "source": "https://github.com/dealerdirect/phpcodesniffer-composer-installer"}, "time": "2022-02-04T12:51:07+00:00"}, {"name": "deployer/deployer", "version": "v7.0.0-rc.8", "source": {"type": "git", "url": "https://github.com/deployphp/deployer.git", "reference": "629973ee3d0ce78953b18500cba0695feec28c2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/deployphp/deployer/zipball/629973ee3d0ce78953b18500cba0695feec28c2b", "reference": "629973ee3d0ce78953b18500cba0695feec28c2b", "shasum": ""}, "bin": ["deployer.phar"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Deployment Tool", "homepage": "https://deployer.org", "support": {"docs": "https://deployer.org/docs", "issues": "https://github.com/deployphp/deployer/issues", "source": "https://github.com/deployphp/deployer"}, "funding": [{"url": "https://github.com/sponsors/antonmedv", "type": "github"}], "time": "2022-03-22T14:33:06+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "mockery/mockery", "version": "1.5.0", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac", "reference": "c10a5f6e06fc2470ab1822fa13fa2a7380f8fbac", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": "^7.3 || ^8.0"}, "conflict": {"phpunit/phpunit": "<8.0"}, "require-dev": {"phpunit/phpunit": "^8.5 || ^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.5.0"}, "time": "2022-01-20T13:18:17+00:00"}, {"name": "nette/tester", "version": "v2.4.2", "source": {"type": "git", "url": "https://github.com/nette/tester.git", "reference": "2e788e243bb17a6889aac5411f3fabc48cc5b23a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nette/tester/zipball/2e788e243bb17a6889aac5411f3fabc48cc5b23a", "reference": "2e788e243bb17a6889aac5411f3fabc48cc5b23a", "shasum": ""}, "require": {"php": ">=7.2 <8.2"}, "require-dev": {"ext-simplexml": "*", "phpstan/phpstan": "^1.0"}, "bin": ["src/tester"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>", "GPL-2.0-only", "GPL-3.0-only"], "authors": [{"name": "<PERSON>", "homepage": "https://davidgrudl.com"}, {"name": "<PERSON><PERSON>", "homepage": "https://github.com/milo"}, {"name": "Nette Community", "homepage": "https://nette.org/contributors"}], "description": "Nette Tester: enjoyable unit testing in PHP with code coverage reporter. 🍏🍏🍎🍏", "homepage": "https://tester.nette.org", "keywords": ["Xdebug", "assertions", "clover", "code coverage", "nette", "pcov", "phpdbg", "phpunit", "testing", "unit"], "support": {"issues": "https://github.com/nette/tester/issues", "source": "https://github.com/nette/tester/tree/v2.4.2"}, "time": "2022-03-24T19:02:37+00:00"}, {"name": "nextras/orm-phpstan", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/nextras/orm-phpstan.git", "reference": "7c230e0b10967ffe0a822845af52d5beab5dcadf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nextras/orm-phpstan/zipball/7c230e0b10967ffe0a822845af52d5beab5dcadf", "reference": "7c230e0b10967ffe0a822845af52d5beab5dcadf", "shasum": ""}, "require": {"php": "~7.1 || ~8.0", "phpstan/phpstan": "^1.0"}, "require-dev": {"nette/tester": "^2.3.1", "nextras/orm": "~4.0 || ~5.0@dev", "phpstan/extension-installer": "^1.1", "phpstan/phpstan-deprecation-rules": "^1.0"}, "type": "phpstan-extension", "extra": {"phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Nextras\\OrmPhpStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan extension for Nextras Orm", "support": {"issues": "https://github.com/nextras/orm-phpstan/issues", "source": "https://github.com/nextras/orm-phpstan/tree/v1.0.0"}, "time": "2021-11-01T07:51:21+00:00"}, {"name": "ninjify/coding-standard", "version": "v0.12.1", "source": {"type": "git", "url": "https://github.com/ninjify/coding-standard.git", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ninjify/coding-standard/zipball/c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "reference": "c655eedbe1b4f9b307e9941ad347f9078fbdd58a", "shasum": ""}, "require": {"php": ">=7.2", "slevomat/coding-standard": "^7.0.18", "squizlabs/php_codesniffer": "^3.5.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Milan <PERSON>", "homepage": "https://f3l1x.io"}], "description": "Tuned & very strict coding standards for PHP projects. Trusted by Contributte, Apitte, Nettrine and many others.", "homepage": "https://github.com/ninjify/coding-standard", "keywords": ["Codestyle", "codesniffer", "ninji<PERSON>", "php"], "support": {"issues": "https://github.com/ninjify/coding-standard/issues", "source": "https://github.com/ninjify/coding-standard/tree/v0.12.1"}, "funding": [{"url": "https://contributte.org/partners.html", "type": "custom"}, {"url": "https://github.com/f3l1x", "type": "github"}], "time": "2022-02-11T14:34:15+00:00"}, {"name": "php-parallel-lint/php-console-color", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Color.git", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Color/zipball/7adfefd530aa2d7570ba87100a99e2483a543b88", "reference": "7adfefd530aa2d7570ba87100a99e2483a543b88", "shasum": ""}, "require": {"php": ">=5.3.2"}, "replace": {"jakub-onderka/php-console-color": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Simple library for creating colored console ouput.", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Color/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Color/tree/v1.0.1"}, "time": "2021-12-25T06:49:29+00:00"}, {"name": "php-parallel-lint/php-console-highlighter", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Console-Highlighter.git", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Console-Highlighter/zipball/5b4803384d3303cf8e84141039ef56c8a123138d", "reference": "5b4803384d3303cf8e84141039ef56c8a123138d", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=5.3.2", "php-parallel-lint/php-console-color": "^1.0.1"}, "replace": {"jakub-onderka/php-console-highlighter": "*"}, "require-dev": {"php-parallel-lint/php-code-style": "^2.0", "php-parallel-lint/php-parallel-lint": "^1.0", "php-parallel-lint/php-var-dump-check": "0.*", "phpunit/phpunit": "^4.8.36 || ^5.7.21 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"PHP_Parallel_Lint\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/issues", "source": "https://github.com/php-parallel-lint/PHP-Console-Highlighter/tree/v1.0.0"}, "time": "2022-02-18T08:23:19+00:00"}, {"name": "php-parallel-lint/php-parallel-lint", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/php-parallel-lint/PHP-Parallel-Lint.git", "reference": "6483c9832e71973ed29cf71bd6b3f4fde438a9de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-parallel-lint/PHP-Parallel-Lint/zipball/6483c9832e71973ed29cf71bd6b3f4fde438a9de", "reference": "6483c9832e71973ed29cf71bd6b3f4fde438a9de", "shasum": ""}, "require": {"ext-json": "*", "php": ">=5.3.0"}, "replace": {"grogy/php-parallel-lint": "*", "jakub-onderka/php-parallel-lint": "*"}, "require-dev": {"nette/tester": "^1.3 || ^2.0", "php-parallel-lint/php-console-highlighter": "0.* || ^1.0", "squizlabs/php_codesniffer": "^3.6"}, "suggest": {"php-parallel-lint/php-console-highlighter": "Highlight syntax in code snippet"}, "bin": ["parallel-lint"], "type": "library", "autoload": {"classmap": ["./src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "This tool check syntax of PHP files about 20x faster than serial check.", "homepage": "https://github.com/php-parallel-lint/PHP-Parallel-Lint", "support": {"issues": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/issues", "source": "https://github.com/php-parallel-lint/PHP-Parallel-Lint/tree/v1.3.2"}, "time": "2022-02-21T12:50:22+00:00"}, {"name": "phpstan/extension-installer", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/phpstan/extension-installer.git", "reference": "66c7adc9dfa38b6b5838a9fb728b68a7d8348051"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/extension-installer/zipball/66c7adc9dfa38b6b5838a9fb728b68a7d8348051", "reference": "66c7adc9dfa38b6b5838a9fb728b68a7d8348051", "shasum": ""}, "require": {"composer-plugin-api": "^1.1 || ^2.0", "php": "^7.1 || ^8.0", "phpstan/phpstan": ">=0.11.6"}, "require-dev": {"composer/composer": "^1.8", "phing/phing": "^2.16.3", "php-parallel-lint/php-parallel-lint": "^1.2.0", "phpstan/phpstan-strict-rules": "^0.11 || ^0.12"}, "type": "composer-plugin", "extra": {"class": "PHPStan\\ExtensionInstaller\\Plugin"}, "autoload": {"psr-4": {"PHPStan\\ExtensionInstaller\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Composer plugin for automatic installation of PHPStan extensions", "support": {"issues": "https://github.com/phpstan/extension-installer/issues", "source": "https://github.com/phpstan/extension-installer/tree/1.1.0"}, "time": "2020-12-13T13:06:13+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.4.3", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "34545bb30a6f8bd86cfa371dbd42140a657bbf0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/34545bb30a6f8bd86cfa371dbd42140a657bbf0d", "reference": "34545bb30a6f8bd86cfa371dbd42140a657bbf0d", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.4.3"}, "time": "2022-04-08T11:30:34+00:00"}, {"name": "phpstan/phpstan", "version": "1.5.4", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "bbf68cae24f6dc023c607ea0f87da55dd9d55c2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/bbf68cae24f6dc023c607ea0f87da55dd9d55c2b", "reference": "bbf68cae24f6dc023c607ea0f87da55dd9d55c2b", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "support": {"issues": "https://github.com/phpstan/phpstan/issues", "source": "https://github.com/phpstan/phpstan/tree/1.5.4"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://www.patreon.com/phpstan", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2022-04-03T12:39:00+00:00"}, {"name": "phpstan/phpstan-deprecation-rules", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-deprecation-rules.git", "reference": "e5ccafb0dd8d835dd65d8d7a1a0d2b1b75414682"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-deprecation-rules/zipball/e5ccafb0dd8d835dd65d8d7a1a0d2b1b75414682", "reference": "e5ccafb0dd8d835dd65d8d7a1a0d2b1b75414682", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "phpstan/phpstan": "^1.0"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "phpstan": {"includes": ["rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan rules for detecting usage of deprecated classes, methods, properties, constants and traits.", "support": {"issues": "https://github.com/phpstan/phpstan-deprecation-rules/issues", "source": "https://github.com/phpstan/phpstan-deprecation-rules/tree/1.0.0"}, "time": "2021-09-23T11:02:21+00:00"}, {"name": "phpstan/phpstan-nette", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan-nette.git", "reference": "f4654b27b107241e052755ec187a0b1964541ba6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan-nette/zipball/f4654b27b107241e052755ec187a0b1964541ba6", "reference": "f4654b27b107241e052755ec187a0b1964541ba6", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "phpstan/phpstan": "^1.0"}, "conflict": {"nette/application": "<2.3.0", "nette/component-model": "<2.3.0", "nette/di": "<2.3.0", "nette/forms": "<2.3.0", "nette/http": "<2.3.0", "nette/utils": "<2.3.0"}, "require-dev": {"nette/forms": "^3.0", "nette/utils": "^2.3.0 || ^3.0.0", "nikic/php-parser": "^4.13.0", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/phpstan-php-parser": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5"}, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "phpstan": {"includes": ["extension.neon", "rules.neon"]}}, "autoload": {"psr-4": {"PHPStan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Nette Framework class reflection extension for PHPStan", "support": {"issues": "https://github.com/phpstan/phpstan-nette/issues", "source": "https://github.com/phpstan/phpstan-nette/tree/1.0.0"}, "time": "2021-09-20T16:12:57+00:00"}, {"name": "roave/security-advisories", "version": "dev-latest", "source": {"type": "git", "url": "https://github.com/Roave/SecurityAdvisories.git", "reference": "880f953435f266cf634bb81fa8888e66fa7814f6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Roave/SecurityAdvisories/zipball/880f953435f266cf634bb81fa8888e66fa7814f6", "reference": "880f953435f266cf634bb81fa8888e66fa7814f6", "shasum": ""}, "conflict": {"3f/pygmentize": "<1.2", "admidio/admidio": "<4.1.9", "adodb/adodb-php": "<=5.20.20|>=5.21,<=5.21.3", "aheinze/cockpit": "<=2.2.1", "akaunting/akaunting": "<2.1.13", "alextselegidis/easyappointments": "<=1.4.3", "alterphp/easyadmin-extension-bundle": ">=1.2,<1.2.11|>=1.3,<1.3.1", "amazing/media2click": ">=1,<1.3.3", "amphp/artax": "<1.0.6|>=2,<2.0.6", "amphp/http": "<1.0.1", "amphp/http-client": ">=4,<4.4", "anchorcms/anchor-cms": "<=0.12.7", "andreapollastri/cipi": "<=3.1.15", "api-platform/core": ">=2.2,<2.2.10|>=2.3,<2.3.6", "appwrite/server-ce": "<0.11.1|>=0.12,<0.12.2", "area17/twill": "<1.2.5|>=2,<2.5.3", "asymmetricrypt/asymmetricrypt": ">=0,<9.9.99", "awesome-support/awesome-support": "<=6.0.7", "aws/aws-sdk-php": ">=3,<3.2.1", "bagisto/bagisto": "<0.1.5", "barrelstrength/sprout-base-email": "<1.2.7", "barrelstrength/sprout-forms": "<3.9", "barryvdh/laravel-translation-manager": "<0.6.2", "baserproject/basercms": "<4.5.4", "billz/raspap-webgui": "<=2.6.6", "bk2k/bootstrap-package": ">=7.1,<7.1.2|>=8,<8.0.8|>=9,<9.0.4|>=9.1,<9.1.3|>=10,<10.0.10|>=11,<11.0.3", "bmarshall511/wordpress_zero_spam": "<5.2.13", "bolt/bolt": "<3.7.2", "bolt/core": "<=4.2", "bottelet/flarepoint": "<2.2.1", "brightlocal/phpwhois": "<=4.2.5", "brotkrueml/codehighlight": "<2.7", "brotkrueml/schema": "<1.13.1|>=2,<2.5.1", "brotkrueml/typo3-matomo-integration": "<1.3.2", "buddypress/buddypress": "<7.2.1", "bugsnag/bugsnag-laravel": ">=2,<2.0.2", "bytefury/crater": "<6.0.2", "cachethq/cachet": "<2.5.1", "cakephp/cakephp": "<3.10.3|>=4,<4.0.6", "cardgate/magento2": "<2.0.33", "cart2quote/module-quotation": ">=4.1.6,<=4.4.5|>=5,<5.4.4", "cartalyst/sentry": "<=2.1.6", "catfan/medoo": "<1.7.5", "centreon/centreon": "<21.4.16|>=21.10,<21.10.8|>=22,<22.4.1", "cesnet/simplesamlphp-module-proxystatistics": "<3.1", "codeception/codeception": "<3.1.3|>=4,<4.1.22", "codeigniter/framework": "<=3.0.6", "codeigniter4/framework": "<4.1.9", "codeigniter4/shield": "= 1.0.0-beta", "codiad/codiad": "<=2.8.4", "composer/composer": "<1.10.26|>=2-alpha.1,<2.2.12|>=2.3,<2.3.5", "concrete5/concrete5": "<9", "concrete5/core": "<8.5.8|>=9,<9.1", "contao-components/mediaelement": ">=2.14.2,<2.21.1", "contao/contao": ">=4,<4.4.56|>=4.5,<4.9.18|>=4.10,<4.11.7|>=4.13,<4.13.3", "contao/core": ">=2,<3.5.39", "contao/core-bundle": "<4.9.18|>=4.10,<4.11.7|>=4.13,<4.13.3|= 4.10.0", "contao/listing-bundle": ">=4,<4.4.8", "contao/managed-edition": "<=1.5", "craftcms/cms": "<3.7.55.2|>= 4.0.0-RC1, < 4.2.1", "croogo/croogo": "<3.0.7", "cuyz/valinor": "<0.12", "czproject/git-php": "<4.0.3", "darylldoyle/safe-svg": "<1.9.10", "datadog/dd-trace": ">=0.30,<0.30.2", "david-garcia/phpwhois": "<=4.3.1", "derhansen/sf_event_mgt": "<4.3.1|>=5,<5.1.1", "directmailteam/direct-mail": "<5.2.4", "doctrine/annotations": ">=1,<1.2.7", "doctrine/cache": ">=1,<1.3.2|>=1.4,<1.4.2", "doctrine/common": ">=2,<2.4.3|>=2.5,<2.5.1", "doctrine/dbal": ">=2,<2.0.8|>=2.1,<2.1.2|>=3,<3.1.4", "doctrine/doctrine-bundle": "<1.5.2", "doctrine/doctrine-module": "<=0.7.1", "doctrine/mongodb-odm": ">=1,<1.0.2", "doctrine/mongodb-odm-bundle": ">=2,<3.0.1", "doctrine/orm": ">=2,<2.4.8|>=2.5,<2.5.1|>=2.8.3,<2.8.4", "dolibarr/dolibarr": "<16|= 12.0.5|>= 3.3.beta1, < 13.0.2", "dompdf/dompdf": "<2.0.1", "drupal/core": ">=7,<7.91|>=8,<9.3.19|>=9.4,<9.4.3", "drupal/drupal": ">=7,<7.80|>=8,<8.9.16|>=9,<9.1.12|>=9.2,<9.2.4", "dweeves/magmi": "<=0.7.24", "ecodev/newsletter": "<=4", "ectouch/ectouch": "<=2.7.2", "elefant/cms": "<1.3.13", "elgg/elgg": "<3.3.24|>=4,<4.0.5", "endroid/qr-code-bundle": "<3.4.2", "enshrined/svg-sanitize": "<0.15", "erusev/parsedown": "<1.7.2", "ether/logs": "<3.0.4", "exceedone/exment": "<4.4.3|>=5,<5.0.3", "exceedone/laravel-admin": "= 3.0.0|<2.2.3", "ezsystems/demobundle": ">=5.4,<5.4.6.1", "ezsystems/ez-support-tools": ">=2.2,<2.2.3", "ezsystems/ezdemo-ls-extension": ">=5.4,<5.4.2.1", "ezsystems/ezfind-ls": ">=5.3,<5.3.6.1|>=5.4,<5.4.11.1|>=2017.12,<2017.12.0.1", "ezsystems/ezplatform": "<=1.13.6|>=2,<=2.5.24", "ezsystems/ezplatform-admin-ui": ">=1.3,<1.3.5|>=1.4,<1.4.6|>=1.5,<1.5.27", "ezsystems/ezplatform-admin-ui-assets": ">=4,<4.2.1|>=5,<5.0.1|>=5.1,<5.1.1", "ezsystems/ezplatform-kernel": "<=1.2.5|>=1.3,<1.3.19", "ezsystems/ezplatform-rest": ">=1.2,<=1.2.2|>=1.3,<1.3.8", "ezsystems/ezplatform-richtext": ">=2.3,<=2.3.7", "ezsystems/ezplatform-user": ">=1,<1.0.1", "ezsystems/ezpublish-kernel": "<=********|>=7,<7.5.29", "ezsystems/ezpublish-legacy": "<=2017.12.7.3|>=2018.6,<=2019.3.5.1", "ezsystems/platform-ui-assets-bundle": ">=4.2,<4.2.3", "ezsystems/repository-forms": ">=2.3,<*******", "ezyang/htmlpurifier": "<4.1.1", "facade/ignition": "<1.16.15|>=2,<2.4.2|>=2.5,<2.5.2", "facturascripts/facturascripts": "<=2022.8", "feehi/cms": "<=2.1.1", "feehi/feehicms": "<=*******", "fenom/fenom": "<=2.12.1", "filegator/filegator": "<7.8", "firebase/php-jwt": "<2", "flarum/core": ">=1,<=1.0.1", "flarum/sticky": ">=0.1-beta.14,<=0.1-beta.15", "flarum/tags": "<=0.1-beta.13", "fluidtypo3/vhs": "<5.1.1", "fof/byobu": ">=0.3-beta.2,<1.1.7", "fof/upload": "<1.2.3", "fooman/tcpdf": "<6.2.22", "forkcms/forkcms": "<5.11.1", "fossar/tcpdf-parser": "<6.2.22", "francoisjacquet/rosariosis": "<10.1", "friendsofsymfony/oauth2-php": "<1.3", "friendsofsymfony/rest-bundle": ">=1.2,<1.2.2", "friendsofsymfony/user-bundle": ">=1.2,<1.3.5", "friendsoftypo3/mediace": ">=7.6.2,<7.6.5", "froala/wysiwyg-editor": "<3.2.7", "froxlor/froxlor": "<0.10.38", "fuel/core": "<1.8.1", "gaoming13/wechat-php-sdk": "<=1.10.2", "genix/cms": "<=1.1.11", "getgrav/grav": "<1.7.34", "getkirby/cms": "<*******|>=3.6,<*******|>=3.7,<3.7.4", "getkirby/panel": "<2.5.14", "getkirby/starterkit": "<=*******", "gilacms/gila": "<=1.11.4", "globalpayments/php-sdk": "<2", "google/protobuf": "<3.15", "gos/web-socket-bundle": "<1.10.4|>=2,<2.6.1|>=3,<3.3", "gree/jose": "<=2.2", "gregwar/rst": "<1.0.3", "grumpydictator/firefly-iii": "<5.6.5", "guzzlehttp/guzzle": "<6.5.8|>=7,<7.4.5", "guzzlehttp/psr7": "<1.8.4|>=2,<2.1.1", "helloxz/imgurl": "= 2.31|<=2.31", "hillelcoren/invoice-ninja": "<5.3.35", "hjue/justwriting": "<=1", "hov/jobfair": "<1.0.13|>=2,<2.0.2", "hyn/multi-tenant": ">=5.6,<5.7.2", "ibexa/core": ">=4,<4.0.7|>=4.1,<4.1.4", "ibexa/post-install": "<=1.0.4", "icecoder/icecoder": "<=8.1", "idno/known": "<=1.3.1", "illuminate/auth": ">=4,<4.0.99|>=4.1,<=4.1.31|>=4.2,<=4.2.22|>=5,<=5.0.35|>=5.1,<=5.1.46|>=5.2,<=5.2.45|>=5.3,<=5.3.31|>=5.4,<=5.4.36|>=5.5,<5.5.10", "illuminate/cookie": ">=4,<=4.0.11|>=4.1,<=4.1.99999|>=4.2,<=4.2.99999|>=5,<=5.0.99999|>=5.1,<=5.1.99999|>=5.2,<=5.2.99999|>=5.3,<=5.3.99999|>=5.4,<=5.4.99999|>=5.5,<=5.5.49|>=5.6,<=5.6.99999|>=5.7,<=5.7.99999|>=5.8,<=5.8.99999|>=6,<6.18.31|>=7,<7.22.4", "illuminate/database": "<6.20.26|>=7,<7.30.5|>=8,<8.40", "illuminate/encryption": ">=4,<=4.0.11|>=4.1,<=4.1.31|>=4.2,<=4.2.22|>=5,<=5.0.35|>=5.1,<=5.1.46|>=5.2,<=5.2.45|>=5.3,<=5.3.31|>=5.4,<=5.4.36|>=5.5,<5.5.40|>=5.6,<5.6.15", "illuminate/view": "<6.20.42|>=7,<7.30.6|>=8,<8.75", "impresscms/impresscms": "<=1.4.3", "in2code/femanager": "<5.5.1|>=6,<6.3.1", "in2code/lux": "<17.6.1|>=18,<24.0.2", "intelliants/subrion": "<=4.2.1", "islandora/islandora": ">=2,<2.4.1", "ivankristianto/phpwhois": "<=4.3", "jackalope/jackalope-doctrine-dbal": "<1.7.4", "james-heinrich/getid3": "<1.9.21", "joomla/archive": "<1.1.12|>=2,<2.0.1", "joomla/filesystem": "<1.6.2|>=2,<2.0.1", "joomla/filter": "<1.4.4|>=2,<2.0.1", "joomla/input": ">=2,<2.0.2", "joomla/session": "<1.3.1", "jsdecena/laracom": "<2.0.9", "jsmitty12/phpwhois": "<5.1", "kazist/phpwhois": "<=4.2.6", "kevinpapst/kimai2": "<1.16.7", "kitodo/presentation": "<3.1.2", "klaviyo/magento2-extension": ">=1,<3", "krayin/laravel-crm": "<1.2.2", "kreait/firebase-php": ">=3.2,<3.8.1", "la-haute-societe/tcpdf": "<6.2.22", "laminas/laminas-diactoros": "<2.11.1", "laminas/laminas-form": "<2.17.1|>=3,<3.0.2|>=3.1,<3.1.1", "laminas/laminas-http": "<2.14.2", "laravel/fortify": "<1.11.1", "laravel/framework": "<6.20.42|>=7,<7.30.6|>=8,<8.75", "laravel/socialite": ">=1,<1.0.99|>=2,<2.0.10", "latte/latte": "<2.10.8", "lavalite/cms": "<=5.8", "lcobucci/jwt": ">=3.4,<3.4.6|>=4,<4.0.4|>=4.1,<4.1.5", "league/commonmark": "<0.18.3", "league/flysystem": "<1.1.4|>=2,<2.1.1", "lexik/jwt-authentication-bundle": "<2.10.7|>=2.11,<2.11.3", "librenms/librenms": "<=22.8", "limesurvey/limesurvey": "<3.27.19", "livehelperchat/livehelperchat": "<=3.91", "livewire/livewire": ">2.2.4,<2.2.6", "lms/routes": "<2.1.1", "localizationteam/l10nmgr": "<7.4|>=8,<8.7|>=9,<9.2", "luyadev/yii-helpers": "<1.2.1", "magento/community-edition": ">=2,<2.2.10|>=2.3,<2.3.3", "magento/magento1ce": "<1.9.4.3", "magento/magento1ee": ">=1,<1.14.4.3", "magento/product-community-edition": ">=2,<2.2.10|>=2.3,<2.3.2-p.2", "marcwillmann/turn": "<0.3.3", "matyhtf/framework": "<3.0.6", "mautic/core": "<4.3|= 2.13.1", "mediawiki/core": ">=1.27,<1.27.6|>=1.29,<1.29.3|>=1.30,<1.30.2|>=1.31,<1.31.9|>=1.32,<1.32.6|>=1.32.99,<1.33.3|>=1.33.99,<1.34.3|>=1.34.99,<1.35", "mezzio/mezzio-swoole": "<3.7|>=4,<4.3", "microweber/microweber": "<=1.3.1", "miniorange/miniorange-saml": "<1.4.3", "mittwald/typo3_forum": "<1.2.1", "modx/revolution": "<= 2.8.3-pl|<2.8", "mojo42/jirafeau": "<4.4", "monolog/monolog": ">=1.8,<1.12", "moodle/moodle": "<4.0.1", "mustache/mustache": ">=2,<2.14.1", "namshi/jose": "<2.2", "neoan3-apps/template": "<1.1.1", "neorazorx/facturascripts": "<2022.4", "neos/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "neos/form": ">=1.2,<4.3.3|>=5,<5.0.9|>=5.1,<5.1.3", "neos/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<2.9.99|>=3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<5.3.10|>=7,<7.0.9|>=7.1,<7.1.7|>=7.2,<7.2.6|>=7.3,<7.3.4|>=8,<8.0.2", "neos/swiftmailer": ">=4.1,<4.1.99|>=5.4,<5.4.5", "netgen/tagsbundle": ">=3.4,<3.4.11|>=4,<4.0.15", "nette/application": ">=2,<2.0.19|>=2.1,<2.1.13|>=2.2,<2.2.10|>=2.3,<2.3.14|>=2.4,<2.4.16|>=3,<3.0.6", "nette/nette": ">=2,<2.0.19|>=2.1,<2.1.13", "nilsteampassnet/teampass": "<=*********", "notrinos/notrinos-erp": "<=0.7", "noumo/easyii": "<=0.9", "nukeviet/nukeviet": "<4.5.2", "nystudio107/craft-seomatic": "<3.4.12", "nzo/url-encryptor-bundle": ">=4,<4.3.2|>=5,<5.0.1", "october/backend": "<1.1.2", "october/cms": "= 1.1.1|= 1.0.471|= 1.0.469|>=1.0.319,<1.0.469", "october/october": ">=1.0.319,<1.0.466|>=2.1,<2.1.12", "october/rain": "<1.0.472|>=1.1,<1.1.2", "october/system": "<1.0.476|>=1.1,<1.1.12|>=2,<2.2.15", "onelogin/php-saml": "<2.10.4", "oneup/uploader-bundle": "<1.9.3|>=2,<2.1.5", "open-web-analytics/open-web-analytics": "<1.7.4", "opencart/opencart": "<=3.0.3.2", "openid/php-openid": "<2.3", "openmage/magento-lts": "<19.4.15|>=20,<20.0.13", "orchid/platform": ">=9,<9.4.4", "oro/commerce": ">=5,<5.0.4", "oro/crm": ">=1.7,<1.7.4|>=3.1,<4.1.17|>=4.2,<4.2.7", "oro/platform": ">=1.7,<1.7.4|>=3.1,<3.1.29|>=4.1,<4.1.17|>=4.2,<4.2.8", "packbackbooks/lti-1-3-php-library": "<5", "padraic/humbug_get_contents": "<1.1.2", "pagarme/pagarme-php": ">=0,<3", "pagekit/pagekit": "<=1.0.18", "paragonie/random_compat": "<2", "passbolt/passbolt_api": "<2.11", "paypal/merchant-sdk-php": "<3.12", "pear/archive_tar": "<1.4.14", "pear/crypt_gpg": "<1.6.7", "pegasus/google-for-jobs": "<1.5.1|>=2,<2.1.1", "personnummer/personnummer": "<3.0.2", "phanan/koel": "<5.1.4", "phpfastcache/phpfastcache": "<6.1.5|>=7,<7.1.2|>=8,<8.0.7", "phpmailer/phpmailer": "<6.5", "phpmussel/phpmussel": ">=1,<1.6", "phpmyadmin/phpmyadmin": "<5.1.3", "phpoffice/phpexcel": "<1.8", "phpoffice/phpspreadsheet": "<1.16", "phpseclib/phpseclib": "<2.0.31|>=3,<3.0.7", "phpservermon/phpservermon": "<=3.5.2", "phpunit/phpunit": ">=4.8.19,<4.8.28|>=5,<5.6.3", "phpwhois/phpwhois": "<=4.2.5", "phpxmlrpc/extras": "<0.6.1", "pimcore/data-hub": "<1.2.4", "pimcore/pimcore": "<=10.5.6", "pocketmine/bedrock-protocol": "<8.0.2", "pocketmine/pocketmine-mp": "<4.7.2|>= 4.0.0-BETA5, < 4.4.2", "pressbooks/pressbooks": "<5.18", "prestashop/autoupgrade": ">=4,<4.10.1", "prestashop/blockwishlist": ">=2,<2.1.1", "prestashop/contactform": ">1.0.1,<4.3", "prestashop/gamification": "<2.3.2", "prestashop/prestashop": ">=********,<*******", "prestashop/productcomments": "<5.0.2", "prestashop/ps_emailsubscription": "<2.6.1", "prestashop/ps_facetedsearch": "<3.4.1", "prestashop/ps_linklist": "<3.1", "privatebin/privatebin": "<1.4", "propel/propel": ">=2-alpha.1,<=2-alpha.7", "propel/propel1": ">=1,<=1.7.1", "pterodactyl/panel": "<1.7", "ptrofimov/beanstalk_console": "<1.7.14", "pusher/pusher-php-server": "<2.2.1", "pwweb/laravel-core": "<=0.3.6-beta", "rainlab/debugbar-plugin": "<3.1", "rankmath/seo-by-rank-math": "<=1.0.95", "react/http": ">=0.7,<1.7", "remdex/livehelperchat": "<3.99", "rmccue/requests": ">=1.6,<1.8", "robrichards/xmlseclibs": "<3.0.4", "rudloff/alltube": "<3.0.3", "s-cart/core": "<6.9", "s-cart/s-cart": "<6.9", "sabberworm/php-css-parser": ">=1,<1.0.1|>=2,<2.0.1|>=3,<3.0.1|>=4,<4.0.1|>=5,<5.0.9|>=5.1,<5.1.3|>=5.2,<5.2.1|>=6,<6.0.2|>=7,<7.0.4|>=8,<8.0.1|>=8.1,<8.1.1|>=8.2,<8.2.1|>=8.3,<8.3.1", "sabre/dav": ">=1.6,<1.6.99|>=1.7,<1.7.11|>=1.8,<1.8.9", "scheb/two-factor-bundle": ">=0,<3.26|>=4,<4.11", "sensiolabs/connect": "<4.2.3", "serluck/phpwhois": "<=4.2.6", "shopware/core": "<=6.4.9", "shopware/platform": "<=6.4.9", "shopware/production": "<=*******", "shopware/shopware": "<=5.7.14", "shopware/storefront": "<=*******", "shopxo/shopxo": "<2.2.6", "showdoc/showdoc": "<2.10.4", "silverstripe/admin": ">=1,<1.8.1", "silverstripe/assets": ">=1,<1.10.1", "silverstripe/cms": "<4.3.6|>=4.4,<4.4.4", "silverstripe/comments": ">=1.3,<1.9.99|>=2,<2.9.99|>=3,<3.1.1", "silverstripe/forum": "<=0.6.1|>=0.7,<=0.7.3", "silverstripe/framework": "<4.10.9", "silverstripe/graphql": "<3.5.2|>=4-alpha.1,<4-alpha.2|= 4.0.0-alpha1", "silverstripe/hybridsessions": ">=1,<2.4.1|>=2.5,<2.5.1", "silverstripe/registry": ">=2.1,<2.1.2|>=2.2,<2.2.1", "silverstripe/restfulserver": ">=1,<1.0.9|>=2,<2.0.4", "silverstripe/silverstripe-omnipay": "<2.5.2|>=3,<3.0.2|>=3.1,<3.1.4|>=3.2,<3.2.1", "silverstripe/subsites": ">=2,<2.1.1", "silverstripe/taxonomy": ">=1.3,<1.3.1|>=2,<2.0.1", "silverstripe/userforms": "<3", "simple-updates/phpwhois": "<=1", "simplesamlphp/saml2": "<1.10.6|>=2,<2.3.8|>=3,<3.1.4", "simplesamlphp/simplesamlphp": "<1.18.6", "simplesamlphp/simplesamlphp-module-infocard": "<1.0.1", "simplito/elliptic-php": "<1.0.6", "slim/slim": "<2.6", "smarty/smarty": "<3.1.47|>=4,<4.2.1", "snipe/snipe-it": "<6.0.11|>= 6.0.0-RC-1, <= 6.0.0-RC-5", "socalnick/scn-social-auth": "<1.15.2", "socialiteproviders/steam": "<1.1", "spipu/html2pdf": "<5.2.4", "spoonity/tcpdf": "<6.2.22", "squizlabs/php_codesniffer": ">=1,<2.8.1|>=3,<3.0.1", "ssddanbrown/bookstack": "<22.2.3", "statamic/cms": "<3.2.39|>=3.3,<3.3.2", "stormpath/sdk": ">=0,<9.9.99", "studio-42/elfinder": "<2.1.59", "subrion/cms": "<=4.2.1", "sulu/sulu": "= 2.4.0-RC1|<1.6.44|>=2,<2.2.18|>=2.3,<2.3.8", "swiftmailer/swiftmailer": ">=4,<5.4.5", "sylius/admin-bundle": ">=1,<1.0.17|>=1.1,<1.1.9|>=1.2,<1.2.2", "sylius/grid": ">=1,<1.1.19|>=1.2,<1.2.18|>=1.3,<1.3.13|>=1.4,<1.4.5|>=1.5,<1.5.1", "sylius/grid-bundle": "<1.10.1", "sylius/paypal-plugin": ">=1,<1.2.4|>=1.3,<1.3.1", "sylius/resource-bundle": "<1.3.14|>=1.4,<1.4.7|>=1.5,<1.5.2|>=1.6,<1.6.4", "sylius/sylius": "<1.9.10|>=1.10,<1.10.11|>=1.11,<1.11.2", "symbiote/silverstripe-multivaluefield": ">=3,<3.0.99", "symbiote/silverstripe-queuedjobs": ">=3,<3.0.2|>=3.1,<3.1.4|>=4,<4.0.7|>=4.1,<4.1.2|>=4.2,<4.2.4|>=4.3,<4.3.3|>=4.4,<4.4.3|>=4.5,<4.5.1|>=4.6,<4.6.4", "symbiote/silverstripe-versionedfiles": "<=2.0.3", "symfont/process": ">=0,<4", "symfony/cache": ">=3.1,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8", "symfony/dependency-injection": ">=2,<2.0.17|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/error-handler": ">=4.4,<4.4.4|>=5,<5.0.4", "symfony/form": ">=2.3,<2.3.35|>=2.4,<2.6.12|>=2.7,<2.7.50|>=2.8,<2.8.49|>=3,<3.4.20|>=4,<4.0.15|>=4.1,<4.1.9|>=4.2,<4.2.1", "symfony/framework-bundle": ">=2,<2.3.18|>=2.4,<2.4.8|>=2.5,<2.5.2|>=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7|>=5.3.14,<=5.3.14|>=5.4.3,<=5.4.3|>=6.0.3,<=6.0.3|= 6.0.3|= 5.4.3|= 5.3.14", "symfony/http-foundation": ">=2,<2.8.52|>=3,<3.4.35|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7", "symfony/http-kernel": ">=2,<2.8.52|>=3,<3.4.35|>=4,<4.2.12|>=4.3,<4.4.13|>=5,<5.1.5|>=5.2,<5.3.12", "symfony/intl": ">=2.7,<2.7.38|>=2.8,<2.8.31|>=3,<3.2.14|>=3.3,<3.3.13", "symfony/maker-bundle": ">=1.27,<1.29.2|>=1.30,<1.31.1", "symfony/mime": ">=4.3,<4.3.8", "symfony/phpunit-bridge": ">=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/polyfill": ">=1,<1.10", "symfony/polyfill-php55": ">=1,<1.10", "symfony/proxy-manager-bridge": ">=2.7,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.1.12|>=4.2,<4.2.7", "symfony/routing": ">=2,<2.0.19", "symfony/security": ">=2,<2.7.51|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.8", "symfony/security-bundle": ">=2,<2.7.48|>=2.8,<2.8.41|>=3,<3.3.17|>=3.4,<3.4.11|>=4,<4.0.11|>=5.3,<5.3.12", "symfony/security-core": ">=2.4,<2.6.13|>=2.7,<2.7.9|>=2.7.30,<2.7.32|>=2.8,<3.4.49|>=4,<4.4.24|>=5,<5.2.9", "symfony/security-csrf": ">=2.4,<2.7.48|>=2.8,<2.8.41|>=3,<3.3.17|>=3.4,<3.4.11|>=4,<4.0.11", "symfony/security-guard": ">=2.8,<3.4.48|>=4,<4.4.23|>=5,<5.2.8", "symfony/security-http": ">=2.3,<2.3.41|>=2.4,<2.7.51|>=2.8,<2.8.50|>=3,<3.4.26|>=4,<4.2.12|>=4.3,<4.3.8|>=4.4,<4.4.7|>=5,<5.0.7|>=5.1,<5.2.8|>=5.3,<5.3.2", "symfony/serializer": ">=2,<2.0.11|>=4.1,<4.4.35|>=5,<5.3.12", "symfony/symfony": ">=2,<3.4.49|>=4,<4.4.35|>=5,<5.3.12|>=5.3.14,<=5.3.14|>=5.4.3,<=5.4.3|>=6.0.3,<=6.0.3", "symfony/translation": ">=2,<2.0.17", "symfony/validator": ">=2,<2.0.24|>=2.1,<2.1.12|>=2.2,<2.2.5|>=2.3,<2.3.3", "symfony/var-exporter": ">=4.2,<4.2.12|>=4.3,<4.3.8", "symfony/web-profiler-bundle": ">=2,<2.3.19|>=2.4,<2.4.9|>=2.5,<2.5.4", "symfony/yaml": ">=2,<2.0.22|>=2.1,<2.1.7", "t3/dce": ">=2.2,<2.6.2", "t3g/svg-sanitizer": "<1.0.3", "tastyigniter/tastyigniter": "<3.3", "tecnickcom/tcpdf": "<6.2.22", "terminal42/contao-tablelookupwizard": "<3.3.5", "thelia/backoffice-default-template": ">=2.1,<2.1.2", "thelia/thelia": ">=2.1-beta.1,<2.1.3", "theonedemon/phpwhois": "<=4.2.5", "thinkcmf/thinkcmf": "<=5.1.7", "tinymce/tinymce": "<5.10", "titon/framework": ">=0,<9.9.99", "topthink/framework": "<=6.0.13", "topthink/think": "<=6.0.9", "topthink/thinkphp": "<=3.2.3", "tribalsystems/zenario": "<9.2.55826", "truckersmp/phpwhois": "<=4.3.1", "twig/twig": "<1.44.7|>=2,<2.15.3|>=3,<3.4.3", "typo3/cms": ">=6.2,<6.2.30|>=7,<7.6.32|>=8,<8.7.38|>=9,<9.5.29|>=10,<10.4.32|>=11,<11.5.16", "typo3/cms-backend": ">=7,<=7.6.50|>=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.13|>=11,<=11.1", "typo3/cms-core": ">=6.2,<=6.2.56|>=7,<7.6.58|>=8,<8.7.48|>=9,<9.5.37|>=10,<10.4.32|>=11,<11.5.16", "typo3/cms-form": ">=8,<=8.7.39|>=9,<=9.5.24|>=10,<=10.4.13|>=11,<=11.1", "typo3/flow": ">=1,<1.0.4|>=1.1,<1.1.1|>=2,<2.0.1|>=2.3,<2.3.16|>=3,<3.0.12|>=3.1,<3.1.10|>=3.2,<3.2.13|>=3.3,<3.3.13|>=4,<4.0.6", "typo3/html-sanitizer": ">=1,<1.0.7|>=2,<2.0.16", "typo3/neos": ">=1.1,<1.1.3|>=1.2,<1.2.13|>=2,<2.0.4|>=2.3,<2.3.99|>=3,<3.0.20|>=3.1,<3.1.18|>=3.2,<3.2.14|>=3.3,<3.3.23|>=4,<4.0.17|>=4.1,<4.1.16|>=4.2,<4.2.12|>=4.3,<4.3.3", "typo3/phar-stream-wrapper": ">=1,<2.1.1|>=3,<3.1.1", "typo3/swiftmailer": ">=4.1,<4.1.99|>=5.4,<5.4.5", "typo3fluid/fluid": ">=2,<2.0.8|>=2.1,<2.1.7|>=2.2,<2.2.4|>=2.3,<2.3.7|>=2.4,<2.4.4|>=2.5,<2.5.11|>=2.6,<2.6.10", "ua-parser/uap-php": "<3.8", "unisharp/laravel-filemanager": "<=2.5.1", "userfrosting/userfrosting": ">=0.3.1,<4.6.3", "usmanhalalit/pixie": "<1.0.3|>=2,<2.0.2", "vanilla/safecurl": "<0.9.2", "verot/class.upload.php": "<=1.0.3|>=2,<=2.0.4", "vrana/adminer": "<4.8.1", "wallabag/tcpdf": "<6.2.22", "wanglelecc/laracms": "<=1.0.3", "web-auth/webauthn-framework": ">=3.3,<3.3.4", "webcoast/deferred-image-processing": "<1.0.2", "wikimedia/parsoid": "<0.12.2", "willdurand/js-translation-bundle": "<2.1.1", "wintercms/winter": "<1.0.475|>=1.1,<1.1.9", "woocommerce/woocommerce": "<6.6", "wp-cli/wp-cli": "<2.5", "wp-graphql/wp-graphql": "<0.3.5", "wpanel/wpanel4-cms": "<=4.3.1", "wwbn/avideo": "<=11.6", "yeswiki/yeswiki": "<4.1", "yetiforce/yetiforce-crm": "<=6.4", "yidashi/yii2cmf": "<=2", "yii2mod/yii2-cms": "<1.9.2", "yiisoft/yii": ">=1.1.14,<1.1.15", "yiisoft/yii2": "<2.0.38", "yiisoft/yii2-bootstrap": "<2.0.4", "yiisoft/yii2-dev": "<2.0.43", "yiisoft/yii2-elasticsearch": "<2.0.5", "yiisoft/yii2-gii": "<2.0.4", "yiisoft/yii2-jui": "<2.0.4", "yiisoft/yii2-redis": "<2.0.8", "yoast-seo-for-typo3/yoast_seo": "<7.2.3", "yourls/yourls": "<=1.8.2", "zendesk/zendesk_api_client_php": "<2.2.11", "zendframework/zend-cache": ">=2.4,<2.4.8|>=2.5,<2.5.3", "zendframework/zend-captcha": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-crypt": ">=2,<2.4.9|>=2.5,<2.5.2", "zendframework/zend-db": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.10|>=2.3,<2.3.5", "zendframework/zend-developer-tools": ">=1.2.2,<1.2.3", "zendframework/zend-diactoros": "<1.8.4", "zendframework/zend-feed": "<2.10.3", "zendframework/zend-form": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-http": "<2.8.1", "zendframework/zend-json": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zend-ldap": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.8|>=2.3,<2.3.3", "zendframework/zend-mail": ">=2,<2.4.11|>=2.5,<2.7.2", "zendframework/zend-navigation": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-session": ">=2,<2.0.99|>=2.1,<2.1.99|>=2.2,<2.2.9|>=2.3,<2.3.4", "zendframework/zend-validator": ">=2.3,<2.3.6", "zendframework/zend-view": ">=2,<2.2.7|>=2.3,<2.3.1", "zendframework/zend-xmlrpc": ">=2.1,<2.1.6|>=2.2,<2.2.6", "zendframework/zendframework": "<=3", "zendframework/zendframework1": "<1.12.20", "zendframework/zendopenid": ">=2,<2.0.2", "zendframework/zendxml": ">=1,<1.0.1", "zetacomponents/mail": "<1.8.2", "zf-commons/zfc-user": "<1.2.2", "zfcampus/zf-apigility-doctrine": ">=1,<1.0.3", "zfr/zfr-oauth2-server-module": "<0.1.2", "zoujingli/thinkadmin": "<6.0.22"}, "default-branch": true, "type": "metapackage", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "Prevents installation of composer packages with known security vulnerabilities: no API, simply require it", "support": {"issues": "https://github.com/Roave/SecurityAdvisories/issues", "source": "https://github.com/Roave/SecurityAdvisories/tree/latest"}, "funding": [{"url": "https://github.com/Ocramius", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/roave/security-advisories", "type": "tidelift"}], "time": "2022-06-25T08:04:47+00:00"}, {"name": "slevomat/coding-standard", "version": "7.1", "source": {"type": "git", "url": "https://github.com/slevomat/coding-standard.git", "reference": "b521bd358b5f7a7d69e9637fd139e036d8adeb6f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/slevomat/coding-standard/zipball/b521bd358b5f7a7d69e9637fd139e036d8adeb6f", "reference": "b521bd358b5f7a7d69e9637fd139e036d8adeb6f", "shasum": ""}, "require": {"dealerdirect/phpcodesniffer-composer-installer": "^0.6.2 || ^0.7", "php": "^7.2 || ^8.0", "phpstan/phpdoc-parser": "^1.4.1", "squizlabs/php_codesniffer": "^3.6.2"}, "require-dev": {"phing/phing": "2.17.2", "php-parallel-lint/php-parallel-lint": "1.3.2", "phpstan/phpstan": "1.4.10|1.5.2", "phpstan/phpstan-deprecation-rules": "1.0.0", "phpstan/phpstan-phpunit": "1.0.0|1.1.0", "phpstan/phpstan-strict-rules": "1.1.0", "phpunit/phpunit": "7.5.20|8.5.21|9.5.19"}, "type": "phpcodesniffer-standard", "extra": {"branch-alias": {"dev-master": "7.x-dev"}}, "autoload": {"psr-4": {"SlevomatCodingStandard\\": "SlevomatCodingStandard"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Slevomat Coding Standard for PHP_CodeSniffer complements Consistence Coding Standard by providing sniffs with additional checks.", "support": {"issues": "https://github.com/slevomat/coding-standard/issues", "source": "https://github.com/slevomat/coding-standard/tree/7.1"}, "funding": [{"url": "https://github.com/kukulich", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/slevomat/coding-standard", "type": "tidelift"}], "time": "2022-03-29T12:44:16+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.6.2", "source": {"type": "git", "url": "https://github.com/squizlabs/PHP_CodeSniffer.git", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/squizlabs/PHP_CodeSniffer/zipball/5e4e71592f69da17871dba6e80dd51bce74a351a", "reference": "5e4e71592f69da17871dba6e80dd51bce74a351a", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0"}, "bin": ["bin/phpcs", "bin/phpcbf"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "lead"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/squizlabs/PHP_CodeSniffer", "keywords": ["phpcs", "standards"], "support": {"issues": "https://github.com/squizlabs/PHP_CodeSniffer/issues", "source": "https://github.com/squizlabs/PHP_CodeSniffer", "wiki": "https://github.com/squizlabs/PHP_CodeSniffer/wiki"}, "time": "2021-12-12T21:44:58+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"contributte/elastica": 20, "deployer/deployer": 5, "roave/security-advisories": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^8.1", "ext-gd": "*", "ext-json": "*", "ext-simplexml": "*", "ext-zip": "*"}, "platform-dev": [], "platform-overrides": {"php": "8.1.4"}, "plugin-api-version": "2.2.0"}