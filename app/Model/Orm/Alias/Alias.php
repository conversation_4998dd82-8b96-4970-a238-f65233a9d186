<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\Traits\HasTreeRepository;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\HasAuthorRepository;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\HasBlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $alias
 * @property string $module {enum self::MODULE_*}
 * @property int $referenceId
 *
 *
 * RELATIONS
 * @property Mutation $mutation {m:1 Mutation::$aliases}
 *
 *
 * VIRTUALS
 * @property-read Tree $parent {virtual}
 * @property-read string $name {virtual}
 */
final class Alias extends Entity
{

	use HasTreeRepository;
	use HasBlogRepository;
	use HasAuthorRepository;

	public const MODULE_TREE = 'tree';
	public const MODULE_BLOG = 'blogLocalization';
	public const MODULE_BLOG_TAG = 'blogTagLocalization';
	public const MODULE_PRODUCT = 'productLocalization';
	public const MODULE_AUTHOR = 'authorLocalization';
	public const MODULE_SEOLINK = 'seoLinkLocalization';
	public const MODULE_PERSON = 'personLocalization';

	public const MODULE_MODAL = 'modalLocalization';


	public const MODULE_REFERENCE = 'referenceLocalization';
	public const MODULE_PRESS = 'pressLocalization';
	public const MODULE_EVENT = 'eventLocalization';
	public const MODULE_DESIGNER = 'designerLocalization';
	public const MODULE_CONTACT = 'contactLocalization';
	public const MODULE_ARTICLE = 'article';

	private BlogLocalizationRepository $blogLocalizationRepository;

	private AuthorLocalizationRepository $authorLocalizationRepository;

	private BlogTagLocalizationRepository $blogTagLocalizationRepository;

	private ProductLocalizationRepository $productLocalizationRepository;

	private SeoLinkLocalizationRepository $seoLinkLocalizationRepository;

	public function injectRepositories(
		BlogTagLocalizationRepository $blogTagLocalizationRepository,
		AuthorLocalizationRepository $authorLocalizationRepository,
		BlogLocalizationRepository $blogLocalizationRepository,
		ProductLocalizationRepository $productLocalizationRepository,
		SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
	): void
	{
		$this->authorLocalizationRepository = $authorLocalizationRepository;
		$this->blogLocalizationRepository = $blogLocalizationRepository;
		$this->blogTagLocalizationRepository = $blogTagLocalizationRepository;
		$this->productLocalizationRepository = $productLocalizationRepository;
		$this->seoLinkLocalizationRepository = $seoLinkLocalizationRepository;
	}

	public function __toString(): string
	{
		return $this->isPersisted() ? $this->alias : '';
	}

	protected function getterParent(): Tree|BlogLocalization|AuthorLocalization|BlogTagLocalization|ProductLocalization|SeoLinkLocalization|null
	{
		$repository = $this->module . 'Repository';
		return $this->$repository->getById($this->referenceId);
	}

	protected function getterName(): string
	{
		return $this->alias;
	}

}
