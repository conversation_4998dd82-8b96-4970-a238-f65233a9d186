<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\AliasHistory\AliasHistoryRepository;
use App\Model\Orm\ApiToken\ApiTokenRepository;
use App\Model\Orm\ApiUser\ApiUserRepository;
use App\Model\Orm\AttachedFile\AttachedFileRepository;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\EmailTemplateFile\EmailTemplateFileRepository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Favourite\FavouriteRepository;
use App\Model\Orm\File\FileRepository;
use App\Model\Orm\Image\ImageRepository;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailRepository;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductFile\ProductFileRepository;
use App\Model\Orm\ProductImage\ProductImageRepository;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\ProductProduct\ProductProductRepository;
use App\Model\Orm\ProductReview\ProductReviewRepository;
use App\Model\Orm\ProductTree\ProductTreeRepository;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationRepository;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\Redirect\RedirectRepository;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\Stock\StockRepository;
use App\Model\Orm\String\StringRepository;
use App\Model\Orm\Supply\SupplyRepository;
use App\Model\Orm\Trash\TrashRepository;
use App\PostType\Blog\Model\Orm\RatingRepository;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use App\PostType\Contact\Model\Orm\ContactRepository;
use App\PostType\Designer\Model\Orm\DesignerLocalizationRepository;
use App\PostType\Designer\Model\Orm\DesignerRepository;
use App\PostType\Event\Model\Orm\EventLocalizationRepository;
use App\PostType\Event\Model\Orm\EventRepository;
use App\PostType\Modal\Model\Orm\ModalLocalizationRepository;
use App\PostType\Modal\Model\Orm\ModalRepository;
use App\PostType\Page\Model\Orm\TreeParentRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\Model\Orm\TreeProduct\TreeProductRepository;
use App\Model\Orm\TreeTree\TreeTreeRepository;
use App\Model\Orm\User\UserRepository;
use App\Model\Orm\UserHash\UserHashRepository;
use App\Model\Orm\UserMutation\UserMutationRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTreeRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use App\PostType\Person\Model\Orm\PersonLocalizationRepository;
use App\PostType\Person\Model\Orm\PersonRepository;
use App\PostType\Press\Model\Orm\PressLocalizationRepository;
use App\PostType\Press\Model\Orm\PressRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\Reference\Model\Orm\ReferenceRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkRepository;
use LogicException;
use Nextras\Orm\Model\Model;
use App\Model\Orm\IpGeolocation\IpGeolocationRepository;

/**
 * @property-read TreeRepository $tree
 * @property-read TreeParentRepository $treeParent
 * @property-read AliasRepository $alias
 * @property-read AttachedFileRepository $attachedFile
 * @property-read LibraryTreeRepository $libraryTree
 * @property-read LibraryImageRepository $libraryImage
 * @property-read UserRepository $user
 * @property-read UserHashRepository $userHash
 * @property-read AliasHistoryRepository $aliasHistory
 * @property-read RedirectRepository $redirect
 * @property-read FileRepository $file
 * @property-read MutationRepository $mutation
 * @property-read EmailTemplateRepository $emailTemplate
 * @property-read EmailTemplateFileRepository $emailTemplateFile
 * @property-read EsIndexRepository $esIndex
 * @property-read TreeTreeRepository $treeTree
 * @property-read StateRepository $state
 * @property-read BlogRepository $blog
 * @property-read BlogLocalizationRepository $blogLocalization
 * @property-read BlogLocalizationTreeRepository $blogLocalizationTree
 * @property-read BlogTagRepository $blogTag
 * @property-read BlogTagLocalizationRepository $blogTagLocalization
 * @property-read AuthorRepository $author
 * @property-read AuthorLocalizationRepository $authorLocalization
 * @property-read PersonLocalizationRepository $personLocalization
 * @property-read PersonRepository $person
 * @property-read UserMutationRepository $userMutation
 * @property-read NewsletterEmailRepository $newsletterEmail
 * @property-read ApiUserRepository $apiUser
 * @property-read StringRepository $string
 * @property-read ParameterRepository $parameter
 * @property-read ParameterValueRepository $parameterValue
 * @property-read PriceLevelRepository $priceLevel
 * @property-read ProductRepository $product
 * @property-read ProductFileRepository $productFile
 * @property-read ProductImageRepository $productImage
 * @property-read ProductLocalizationRepository $productLocalization
 * @property-read ProductProductRepository $productProduct
 * @property-read ProductReviewRepository $productReview
 * @property-read ProductTreeRepository $productTree
 * @property-read ProductVariantRepository $productVariant
 * @property-read ProductVariantLocalizationRepository $productVariantLocalization
 * @property-read ProductVariantPriceRepository $productVariantPrice
 * @property-read StockRepository $stock
 * @property-read SupplyRepository $supply
 * @property-read TreeProductRepository $treeProduct
 * @property-read SeoLinkRepository $seoLink
 * @property-read SeoLinkLocalizationRepository $seoLinkLocalization
 * @property-read ModalRepository $modal
 * @property-read ModalLocalizationRepository $modalLocalization
 * @property-read ApiTokenRepository $apiToken
 * @property-read ContactRepository $contact
 * @property-read ContactLocalizationRepository $contactLocalization
 * @property-read DesignerRepository $designer
 * @property-read DesignerLocalizationRepository $designerLocalization
 * @property-read ReferenceRepository $reference
 * @property-read ReferenceLocalizationRepository $referenceLocalization
 * @property-read PressRepository $press
 * @property-read PressLocalizationRepository $pressLocalization
 * @property-read EventRepository $event
 * @property-read EventLocalizationRepository $eventLocalization
 * @property-read RatingRepository $rating
 * @property-read TrashRepository $trash
 * @property-read IpGeolocationRepository $ipGeolocation
 */
final class Orm extends Model
{

	private ?Mutation $selectedMutation = null;

	private bool $globalPublicOnly = true;

	public function setMutation(Mutation $mutation): void
	{
		$this->selectedMutation = $mutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->selectedMutation === null) {
			throw new LogicException('ORM mutation setup missing.');
		}

		return $this->selectedMutation;
	}


	public function hasMutation(): bool
	{
		return $this->selectedMutation !== null;
	}


	public function setPublicOnly(bool $globalPublicOnly = true): void
	{
		$this->globalPublicOnly = $globalPublicOnly;
	}


	public function getPublicOnly(): bool
	{
		return $this->globalPublicOnly;
	}

}
