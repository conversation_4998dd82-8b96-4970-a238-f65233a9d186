<?php declare(strict_types = 1);

namespace App\Model\Orm\State;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasFormDefaultData;
use App\Model\Orm\User\User;
use App\PostType\Contact\Model\Orm\Contact;
use App\PostType\Modal\Model\Orm\Modal;
use App\PostType\Person\Model\Orm\Person;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $code iso2
 * @property string $name
 * @property VatRates $vatRates {embeddable}
 * @property int $public {default 1}
 *
 * RELATIONS
 * @property Mutation[]|ManyHasMany $mutations {m:m Mutation::$states}
 * @property User[]|OneHasMany|null $users {1:m User::$state}
 * @property Contact[]|ManyHasMany $contacts {M:M Contact::$states, orderBy=[id=ASC]}
 * @property Person[]|ManyHasMany $persons {M:M Person::$states, orderBy=[id=ASC]}
 * @property Modal[]|ManyHasMany $modals {M:M Modal::$states, orderBy=[id=ASC]}
 */
class State extends BaseEntity
{

	use HasFormDefaultData;

	public const DEFAULT_ID = 1;
	public const DEFAULT_CODE = 'CZ';

	public const COOKIE_NAME_SELECTED_STATE = 'selectedState';

}
