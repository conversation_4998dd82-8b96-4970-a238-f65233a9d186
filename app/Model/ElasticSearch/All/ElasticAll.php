<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\All;

use App\Model\ElasticSearch\Entity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Product\Product;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Designer\Model\Orm\DesignerLocalization;
use App\PostType\Event\Model\Orm\EventLocalization;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Person\Model\Orm\PersonLocalization;
use App\PostType\Press\Model\Orm\PressLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use LogicException;

/**
 * @param Convertor[] $convertors
 */
class ElasticAll implements Entity
{

	public const TYPE_TREE = 'tree';
	public const TYPE_BLOG = 'blogLocalization';
	public const TYPE_BLOG_TAG = 'blogTagLocalization';
	public const TYPE_AUTHOR = 'authorLocalization';
	public const TYPE_PRODUCT = 'product';
	public const TYPE_SEOLINK = 'seoLinkLocalization';
	public const TYPE_DESIGNER = 'designerLocalization';
	public const TYPE_REFERENCE = 'referenceLocalization';
	public const TYPE_EVENT = 'eventLocalization';
	public const TYPE_PRESS = 'pressLocalization';
	const TYPE_PERSON = 'personLocalization';
	const TYPE_CONTACT = 'contactLocalization';
	const TYPE_MODAL = 'modalLocalization';

	public function __construct(
		private object $object,
		private array $convertors = [],
	)
	{
	}

	public function getId(): string
	{
		$class = get_class($this->object);

		if (isset($this->object->id)) {
			return match ($class) {
				BlogLocalization::class => self::TYPE_BLOG . '-' . $this->object->id,
				BlogTagLocalization::class => self::TYPE_BLOG_TAG . '-' . $this->object->id,
				AuthorLocalization::class => self::TYPE_AUTHOR . '-' . $this->object->id,
				DesignerLocalization::class => self::TYPE_DESIGNER . '-' . $this->object->id,
				CommonTree::class, CatalogTree::class => self::TYPE_TREE . '-' . $this->object->id,
				Product::class => self::TYPE_PRODUCT . '-' . $this->object->id,
				SeoLinkLocalization::class => self::TYPE_SEOLINK . '-' . $this->object->id,
				ReferenceLocalization::class => self::TYPE_REFERENCE . '-' . $this->object->id,
				EventLocalization::class => self::TYPE_EVENT . '-' . $this->object->id,
				PressLocalization::class => self::TYPE_PRESS . '-' . $this->object->id,
				PersonLocalization::class => self::TYPE_PERSON . '-' . $this->object->id,
				ContactLocalization::class => self::TYPE_CONTACT . '-' . $this->object->id,
				ModalLocalization::class => self::TYPE_MODAL . '-' . $this->object->id,
				default => throw new LogicException(sprintf("Missing definition for '%s' class", $class))
			};
		} else {
			throw new LogicException('Missing primary key for entity');
		}
	}

	public function getData(Mutation $mutation): array
	{
		$convertedData = [];
		foreach ($this->convertors as $convertor) {
			$convertedData[] = $convertor->convert($this->object);
		}

		return array_merge(...$convertedData);
	}

}
