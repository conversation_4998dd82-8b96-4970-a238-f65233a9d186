<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use App\PostType\Designer\Model\Orm\DesignerLocalizationRepository;
use App\PostType\Event\Model\Orm\EventLocalizationRepository;
use App\PostType\Modal\Model\Orm\ModalLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Person\Model\Orm\PersonLocalizationRepository;
use App\PostType\Press\Model\Orm\PressLocalizationRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use LogicException;
use Nextras\Orm\Entity\IEntity;

class ClonerProvider
{

	public function __construct(
		private readonly TreeRepository $treeRepository,
		private readonly ProductLocalizationRepository $productLocalizationRepository,
		private readonly BlogTagLocalizationRepository $blogTagLocalizationRepository,
		private readonly AuthorLocalizationRepository $authorLocalizationRepository,
		private readonly SeoLinkLocalizationRepository $seoLinkLocalizationRepository,
		private readonly BlogLocalizationRepository $blogLocalizationRepository,
		private readonly ReferenceLocalizationRepository $referenceLocalizationRepository,
		private readonly EventLocalizationRepository $eventLocalizationRepository,
		private readonly PressLocalizationRepository $pressLocalizationRepository,
		private readonly PersonLocalizationRepository $personLocalizationRepository,
		private readonly ModalLocalizationRepository $modalLocalizationRepository,
		private readonly PageCommonCloner $pageCommonCloner,
		private readonly ProductLocalizationCommonCloner $productLocalizationCommonCloner,
		private readonly BlogTagLocalizationCommonCloner $blogTagLocalizationCommonCloner,
		private readonly AuthorLocalizationCommonCloner $authorLocalizationCommonCloner,
		private readonly SeoLinkLocalizationCommonCloner $seoLinkLocalizationCommonCloner,
		private readonly BlogLocalizationCommonCloner $blogLocalizationCommonCloner,
		private readonly DesignerLocalizationRepository $designerLocalizationRepository,
		private readonly DesignerLocalizationCommonCloner $designerLocalizationCommonCloner,
		private readonly ContactLocalizationRepository $contactLocalizationRepository,
		private readonly ContactLocalizationCommonCloner $contactLocalizationCommonCloner,
		private readonly ReferenceLocalizationCommonCloner $referenceLocalizationCommonCloner,
		private readonly EventLocalizationCommonCloner $eventLocalizationCommonCloner,
		private readonly PressLocalizationCommonCloner $pressLocalizationCommonCloner,
		private readonly PersonLocalizationCommonCloner $personLocalizationCommonCloner,
		private readonly ModalLocalizationCommonCloner $modalLocalizationCommonCloner,
	)
	{}

	public function getClonerByEntity(IEntity $entity): CommonCloner
	{
		$entityClass = get_class($entity);

		return match (true) {
			in_array($entityClass, $this->treeRepository->getEntityClassNames()) => $this->pageCommonCloner,
			in_array($entityClass, $this->productLocalizationRepository->getEntityClassNames()) => $this->productLocalizationCommonCloner,
			in_array($entityClass, $this->blogTagLocalizationRepository->getEntityClassNames()) => $this->blogTagLocalizationCommonCloner,
			in_array($entityClass, $this->authorLocalizationRepository->getEntityClassNames()) => $this->authorLocalizationCommonCloner,
			in_array($entityClass, $this->seoLinkLocalizationRepository->getEntityClassNames()) => $this->seoLinkLocalizationCommonCloner,
			in_array($entityClass, $this->blogLocalizationRepository->getEntityClassNames()) => $this->blogLocalizationCommonCloner,
			in_array($entityClass, $this->designerLocalizationRepository->getEntityClassNames()) => $this->designerLocalizationCommonCloner,
			in_array($entityClass, $this->contactLocalizationRepository->getEntityClassNames()) => $this->contactLocalizationCommonCloner,
			in_array($entityClass, $this->referenceLocalizationRepository->getEntityClassNames()) => $this->referenceLocalizationCommonCloner,
			in_array($entityClass, $this->eventLocalizationRepository->getEntityClassNames()) => $this->eventLocalizationCommonCloner,
			in_array($entityClass, $this->pressLocalizationRepository->getEntityClassNames()) => $this->pressLocalizationCommonCloner,
			in_array($entityClass, $this->personLocalizationRepository->getEntityClassNames()) => $this->personLocalizationCommonCloner,
			in_array($entityClass, $this->modalLocalizationRepository->getEntityClassNames()) => $this->modalLocalizationCommonCloner,
			default => throw new LogicException(sprintf("Missing definition of Cloner for '%s' entity", $entityClass))
		};
	}

}
