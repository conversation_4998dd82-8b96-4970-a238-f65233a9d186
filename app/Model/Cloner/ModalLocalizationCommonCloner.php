<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Modal\Model\Orm\ModalLocalizationRepository;
use App\PostType\Modal\Model\Orm\ModalRepository;
use Nextras\Orm\Entity\IEntity;

class ModalLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly ModalLocalizationRepository $modalLocalizationRepository,
		private readonly ModalRepository $modalRepository,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation): ModalLocalization
	{
		assert($entity instanceof ModalLocalization);
		$newModalLocalization = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->modalLocalizationRepository,
			$this->modalRepository,
			'modal'
		);
		assert($newModalLocalization instanceof ModalLocalization);

		$newModalLocalization->public = false;

		return $newModalLocalization;
	}

}
