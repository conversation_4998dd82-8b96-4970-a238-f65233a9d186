cf:
	fields:
		parameters:
			type: group
			label: 'Parametry'
			items:
				paramInterior:
					type: list
					label: "Interiér"
					items:
						interior:
							type: suggest
							subType: parameterValue
							label: "Interiér"
							url: @cf.suggestUrls.searchParameterValueByUid.interior
				paramType:
					type: suggest
					subType: parameterValue
					label: "Typ (kategorie)"
					url: @cf.suggestUrls.searchParameterValueByUid.typeReferences
				paramLocation:
					type: suggest
					subType: parameterValue
					label: "Světadíl"
					url: @cf.suggestUrls.searchParameterValueByUid.location

		logo:
			type: image
			label: "Logo v úvodní sekci"
		intro_files:
			@cf.definitions.introFiles
		colorTheme:
			type: group
			label: "Výběr barevné varianty stránky"
			items:
				colorTheme:
					@cf.definitions.colorTheme
		location:
			type: group
			label: "Lokalita"
			items:
				city:
					type: text
					label: "Město"
				state:
					type: text
					label: "<PERSON>át"
	templates:
		reference:
			- @cf.inverted_header
			- @cf.parameters
			img_crossroad:
				extends: @cf.img_crossroad
				label: "<PERSON>brázek pro výpis (3840x3840)"
			- @cf.logo
			introMedia:
				type: group
				label: "Medium pro úvodní sekci"
				items:
					imgs:
						type: group
						label: "Obrázky do úvodní sekce"
						items:
							img_desktop:
								type: image
								label: "Desktop (2192x1212)"
							img_tablet:
								type: image
								label: "Tablet (1500x975)"
							img_mobile:
								type: image
								label: "Mobil (750x712)"
					youtube:
						type: text
						label: "Odkaz na Youtube / Vimeo"
					file:
						type: file
						label: "Videosoubor"
					autoplay:
						type: checkbox
						label: "Automatické přehrávání + loop"
					poster:
						type: image
						label: "Zástupný obrázek videa (1920x960)"
			- @cf.social_images
		referenceLocalization: [@cf.intro_files, @cf.colorTheme, @cf.location]


cc:
	templates:
		"referenceLocalization": *


application:
	mapping:
		Reference: App\PostType\Reference\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Reference: reference
	config:
		reference:
			paging: 12

services:

	- App\PostType\Reference\AdminModule\Components\Form\Builder
	- App\PostType\Reference\AdminModule\Components\Form\Handler
	- App\PostType\Reference\Model\ReferenceLocalizationFacade
	- App\PostType\Reference\AdminModule\Components\Form\FormFactory
	- App\Model\Cloner\ReferenceLocalizationCommonCloner

