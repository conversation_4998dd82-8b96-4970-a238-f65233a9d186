{varType App\PostType\Person\Model\Orm\PersonLocalization $entityLocalization}
{varType App\PostType\Person\Model\Orm\Person $parent}

<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include $corePartsDirectory . '/header.latte', entityLocalization => $entityLocalization}
	</div>

	<div class="main__content scroll">
		{include './parts/content/content.latte', form => $form}
		{include './parts/content/states.latte', form => $form}
		{include $corePartsDirectory . '/content/seo.latte', form => $form}
		{include $corePartsDirectory . '/content/coreCustomItems.latte', form => $form}
	</div>



	<div class="main__content-side scroll">
		{include $corePartsDirectory . '/side/btns.latte'}
		{include $corePartsDirectory . '/side/state.latte', form => $form}
		{include './parts/side/settings.latte', form => $form}
		{control language}

		{include $corePartsDirectory . '/side/edits.latte'}
	</div>

	{capture $newItemTemplate}
		{include './parts/newItemTemplate.latte', form=>$form}
	{/capture}

</form>

{$newItemTemplate}

{include $templates . '/part/core/libraryOverlay.latte'}


