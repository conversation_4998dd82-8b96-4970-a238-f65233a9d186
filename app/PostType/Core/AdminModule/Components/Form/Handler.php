<?php declare(strict_types = 1);

namespace App\PostType\Core\AdminModule\Components\Form;

use App\Model\CustomContent\CustomContent;
use App\Model\CustomField\CustomFields;
use App\Model\Orm\Alias\AliasModel;
use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\User\User;
use App\PostType\Author\Model\Orm\AuthorLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalization;
use App\PostType\Contact\Model\Orm\ContactLocalization;
use App\PostType\Core\AdminModule\Components\Form\FormData\BaseFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\LocalizationFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ParentFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\PublishFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\RoutableFormData;
use App\PostType\Core\AdminModule\Components\Form\FormData\ValidityFormData;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use App\PostType\Designer\Model\Orm\DesignerLocalization;
use App\PostType\Event\Model\Orm\EventLocalization;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Person\Model\Orm\PersonLocalization;
use App\PostType\Press\Model\Orm\PressLocalization;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\IRepository;

final class Handler
{

	public function __construct(
		private readonly AliasModel $aliasModel,
		private readonly CustomFields $customFields,
		private readonly CustomContent $customContent,
		private readonly Orm $orm,
	)
	{
	}


	public function handle(LocalizationEntity $entityLocalization, User $user, BaseFormData $data): void
	{
		$this->handleLocalization($entityLocalization, $data->localization);
        if ($entityLocalization instanceof Routable) {
            $this->handleRoutable($entityLocalization, $data->localization, $data->routable, $entityLocalization->getMutation());
        }

        if ($entityLocalization instanceof Publishable) {
            $this->handlePublish($entityLocalization, $data->publish);
        }
		$this->handleParent($entityLocalization->getParent(), $data->parent);

        if ($entityLocalization instanceof AuthorLocalization
            || $entityLocalization instanceof BlogTagLocalization
            || $entityLocalization instanceof BlogLocalization
            || $entityLocalization instanceof DesignerLocalization
            || $entityLocalization instanceof PressLocalization
            || $entityLocalization instanceof ReferenceLocalization
            || $entityLocalization instanceof EventLocalization
            || $entityLocalization instanceof PersonLocalization
            || $entityLocalization instanceof ContactLocalization) {

            $this->handleEditor($entityLocalization, $user);
        }



		if ($entityLocalization instanceof Validatable) {
			$this->handleValidity($entityLocalization, $data->validity);
		}

        assert($entityLocalization instanceof IEntity);
		$this->orm->persistAndFlush($entityLocalization);
	}


	public function handleLocalization(LocalizationEntity $entityLocalization, LocalizationFormData $data): void
	{
		$entityLocalization->setName($data->name);

		if (isset($entityLocalization->cf)) {
			if (isset($data->cf) && $data->cf !== '') {
				$entityLocalization->setCf($this->customFields->prepareDataToSave($data->cf));
			} else {
				$entityLocalization->setCf(new ArrayHash());
			}
		}

		if (isset($entityLocalization->cc)) {
			if (isset($data->cc) && $data->cc !== ''
				&& isset($data->ccScheme) && $data->ccScheme !== '') {
				$entityLocalization->setCc($this->customContent->prepareDataToSave($data->cc, $data->ccScheme));
			} else {
				$entityLocalization->setCc(new ArrayHash());
			}
		}
	}

	public function handleRoutable(Routable $entityLocalization, LocalizationFormData $localization, RoutableFormData $routable, Mutation $mutation): void
	{
		$entityLocalization->setNameTitle($routable->nameTitle);
		$entityLocalization->setNameAnchor($routable->nameAnchor);

		$entityLocalization->setDescription($routable->description);
		$entityLocalization->setKeywords($routable->keywords);
		$entityLocalization->setAliasHistoryString($routable->aliasHistory);

        $entityLocalization->setAlias($routable->alias ?: $this->aliasModel->generateAlias($localization->name, $mutation, $entityLocalization));
	}

	public function handlePublish(Publishable $entityLocalization, PublishFormData $data): void
	{
		$entityLocalization->setIsPublic($data->public);
	}


	public function handleValidity(Validatable $entityLocalization, ValidityFormData $data): void
	{
		$entityLocalization->setPublicFrom($data->publicFromDateTime);
		$entityLocalization->setPublicTo($data->publicToDateTime);
	}

	public function handleEditor(AuthorLocalization|BlogTagLocalization|BlogLocalization|DesignerLocalization
	|PressLocalization|ReferenceLocalization|EventLocalization|PersonLocalization|ContactLocalization|ModalLocalization $entityLocalization, User $user): void
	{
		$entityLocalization->edited = $user->id;
		$entityLocalization->editedTime = new DateTimeImmutable();
	}

	public function handleParent(ParentEntity $localizableEntity, ParentFormData $data): void
	{
		$localizableEntity->setInternalName($data->internalName);
		if (isset($localizableEntity->cf) && isset($data->cf) && $data->cf !== '') {
			$localizableEntity->setCf($this->customFields->prepareDataToSave($data->cf));
		}
	}

	public function handleRelations(IRepository $repository, IEntity $entity, ArrayHash $values, string $relationName): void
	{
		$selectedIds = [];
		if (isset($values->$relationName)) {
			foreach ($values->$relationName as $postRow) {
				$selectedEntity = $repository->getById($postRow->id);
				if ($selectedEntity !== null) {
					bd($selectedEntity);
					assert($selectedEntity instanceof BaseEntity);
					$selectedIds[$selectedEntity->id] = $selectedEntity->id;
				}
			}
		}
		$entity->$relationName->set($selectedIds);
	}

}
