parameters:
	config:
		blog:
			paging: 12

	postTypeRoutes:
		Blog: blog

cf:
	templates:
		blogLocalization:
			- @cf.inverted_header
#			- @cf.colorTheme

			colorTheme:
				type: group
				label: "Výběr barevné varianty stránky"
				items:
					colorTheme:
						@cf.definitions.colorTheme

			img_crossroad:
				type: image
				label: "Obrázek pro výpis (910x607)"
			- @cf.imgs_article
			- @cf.social_images
			video:
				type: group
				label: "Video do úvodní sekce"
				items:
					link:
						type: text
						label: "Odkaz na Youtube / Vimeo"
					autoplay:
						type: checkbox
						label: "Automatické přehrávání + loop"
					poster:
						type: image
						label: "Zástupný obrázek videa (1920x1080)"
			calendar:
				type: group
				label: "Odkaz na přidání do kalendáře"
				items:
					link:
						type: text

cc:
	templates:


application:
	mapping:
		Blog: App\PostType\Blog\*Module\Presenters\*Presenter

services:
	- App\PostType\Blog\AdminModule\Components\Form\Builder
	- App\PostType\Blog\AdminModule\Components\Form\Handler
	- App\PostType\Blog\Model\Orm\BlogLocalizationModel
	- App\PostType\Blog\Model\BlogLocalizationFacade
	- App\PostType\Blog\AdminModule\Components\Form\FormFactory
	- App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory

	- App\FrontModule\Components\Rating\RatingFactory
	- App\PostType\Blog\Model\Orm\RatingModel

	-
		implement: App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory
		inject: true
