{varType App\PostType\Blog\Model\Orm\BlogLocalization $object}

{block content}
	{include $templates.'/part/box/intro-article.latte', imgs=>$object->cf->imgs_article ?? false}

	<div class="row-main">
		<div class="content-indented u-mb-11 u-mb-15@md u-mb-18@lg">
			<div class="b-article-info">
				{if $object->authors}
					<ul class="b-article-info__authors">
						{foreach $object->authors as $author}
							<li class="b-article-info__author">
								{include $templates.'/part/box/author-sm.latte', author=>$author}
							</li>
						{/foreach}
					</ul>
				{/if}
				<p class="b-article-info__date u-font-label u-mb-0">
					{$object->publicFrom|date:"j. n. Y"}
				</p>
			</div>
		</div>

		{control customContentRenderer}

		<div class="b-article-info-bottom u-mb-6">
				<div class="b-article-info-bottom__content content-indented">
					{include $templates.'/part/box/rating.latte'}
					{*{include $templates.'/part/crossroad/tags.latte', spacingBottom=>0, spacingBottom=>0, spacingBottomLg=>0, crossroad=>$object->blogTags, customTitle=>title_article_tags}*}
				</div>
		</div>

		<p n:if="$object->cf->calendar->link ?? false" class="u-text-right u-mb-6">
			<a href="{$object->cf->calendar->link}" class="btn">
				<span class="btn__text">
					{_"btn_add_to_calendar"}
				</span>
			</a>
		</p>
		{php $colorTheme = $object->cf->colorTheme->colorTheme ?? false}
		{include $templates.'/part/box/share.latte', modifier=>$colorTheme == 'brown' ? 'b-share--mid-gold'}
		{include $templates.'/part/crossroad/authors-list.latte', crossroad=>$object->authors}
		{include $templates.'/part/crossroad/articles.latte', crossroad=>$relatedBlogs, pager=>false, button=>$pages->blog, customTitle=>"title_attached_articles"}
		{*{include $templates.'/part/crossroad/tags.latte', crossroad=>$mostUsedBlogTags}*}

	</div>


{/block}
