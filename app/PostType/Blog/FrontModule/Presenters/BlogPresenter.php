<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\Rating\RatingFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogs;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredData;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory;
use App\PostType\BlogTag\Model\BlogTagModel;
use ArrayIterator;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use function Clue\StreamFilter\fun;

/**
 * @method Blog getObject()
 */
final class BlogPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private BlogLocalization $blogLocalization;
	/**
	 * @var BlogLocalization[]|ICollection
	 */
	private ICollection $blogs;

	public function __construct(
		private readonly AttachedBlogsFactory $attachedBlogsFactory,
		private readonly BlogLocalizationModel $blogLocalizationModel,
		private readonly BlogLocalizationStructuredDataFactory $blogLocalizationStructuredDataFactory,
		private readonly BlogTagModel $blogTagModel,

	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
		$possibleBlogIds = $this->orm->blogLocalization->findByIdInPathString($object)->fetchPairs(null, 'id');
		$this->blogs = $this->orm->blogLocalization->findBy(['id' => $possibleBlogIds])
			->orderBy('isTop', ICollection::DESC)
			->orderBy('publicFrom', ICollection::DESC);
	}


	public function renderDefault(CommonTree $object): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$crossroadBlogCounts = [];
		$crossroadIdsToSkip = [];
		foreach ($object->crossroad as $page) {

			$publicBlogsCount = $this->orm->blogLocalization->findBy(['id' => $page->blogs->fetchPairs(null, 'id')])->countStored();
			if ($publicBlogsCount === 0) {
				$crossroadIdsToSkip[$page->id] = $page->id;
			}
			$crossroadBlogCounts[$page->id] = '('.$publicBlogsCount.')';

		}

		$paginator = $this['pager']->getPaginator();

		$paginator->itemsPerPage = $this->configService->get('blog', 'paging');
		// nastaven stejny paging na prvni strance s ostatnimi strankami
		$paginator->itemsOnFirstPage = $paginator->itemsPerPage;

		$totalCount = $this->blogs->countStored();
		$paginator->itemCount = $totalCount;

		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation);
		$this->template->sufixTexts = $crossroadBlogCounts;
		$this->template->crossroadIdsToSkip = $crossroadIdsToSkip;
		$this->template->totalCount = $totalCount;
		$this->template->blogs = $this->blogs->limitBy($paginator->itemsPerPage, $paginator->offset);
		if ($this->enforcedTemplateForGallery) {
			// render plain html
		} else {
			if ($this->isAjax()) {

				if ($this['pager']->getParameter('more')) {
					$this->redrawControl('blogs');
					$this->redrawControl('articleList');
					$this->redrawControl('articlesPagerBottom');
				} else {
					if (!$this->getSignal()) {
						$this->redrawControl('contentModal');
					}
				}
			}
		}

	}

	public function actionDetail(BlogLocalization $object): void
	{
		$this->setObject($object);
		$this->blogLocalization = $object;

		$this->template->relatedBlogs = $this->getRelatedItems($object);
	}



	public function renderDetail(): void
	{
		$this->blogLocalizationModel->increaseViews($this->blogLocalization);

		$this->template->blogLocalization = $this->blogLocalization;
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		if (!$this->enforcedTemplateForGallery) {
			$this->template->setFile(__DIR__ . '/../templates/' . $this->action . '.latte');
		}
	}

	public function getRelatedItems(BlogLocalization $object): ICollection
	{
		$resultItems = [];
		$maxItemCount = 4;

		// find attached
		$attachedItems = $object->attachedBlogs
			->findBy(['mutation' => $this->mutation->id])
			->orderBy('publicFrom', ICollection::DESC)
			->limitBy($maxItemCount);

		foreach ($attachedItems as $item) {
			$resultItems[$item->id] = $item;
		}

		// not enought -> find related
		$neededItemCount = $maxItemCount - count($resultItems);
		if ($neededItemCount > 0) {
			$itemCategories = $object->categories;
			foreach ($itemCategories as $ic) {
				$notThese = array_keys($resultItems);
				$notThese[] = $object->id;
				foreach ($ic->blogs->findBy([
					'mutation' => $this->mutation->id,
					'id!=' => $notThese,
				])->orderBy('publicFrom', ICollection::DESC) as $item) {
					$resultItems[$item->id] = $item;
					if (count($resultItems) >= $maxItemCount) {
						break 2;
					}
				}
			}

			// not enought -> find more related
			$neededItemCount = $maxItemCount - count($resultItems);
			if ($neededItemCount > 0) {
				$itemTags = $object->blogTags;
				foreach ($itemTags as $it) {
					$notThese = array_keys($resultItems);
					$notThese[] = $object->id;
					foreach ($it->blogs->findBy([
						'mutation' => $this->mutation->id,
						'id!=' => $notThese
					])->orderBy('publicFrom', ICollection::DESC) as $item) {
						$resultItems[$item->id] = $item;
						if (count($resultItems) >= $maxItemCount) {
							//enough
							break 2;

						}
					}

				}
			}

			// not enought -> find some other
			$neededItemCount = $maxItemCount - count($resultItems);
			if ($neededItemCount > 0) {
				$notThese = array_keys($resultItems);
				$notThese[] = $object->id;
				$otherItems = $this->orm->blogLocalization
					->findBy([
						'public' => 1,
						'mutation' => $this->mutation->id,
						'id!=' => $notThese
					])
					->orderBy('publicFrom', ICollection::DESC)
					->limitBy($neededItemCount);
				foreach ($otherItems as $oi) {
					$resultItems[$oi->id] = $oi;
				}
			}
		}

		$ids = array_map(function (BlogLocalization $blogLocalization) {
			return $blogLocalization->id;
		}, $resultItems);

		if ($ids === []) {
			return new EmptyCollection();
		}

		return $this->orm->blogLocalization->findByIds($ids)->orderBy('publicFrom', ICollection::DESC);
	}


	protected function createComponentAttachedBlogs(): AttachedBlogs
	{
		return $this->attachedBlogsFactory->create($this->blogLocalization->blog);
	}

	protected function createComponentBlogLocalizationStructuredData(): BlogLocalizationStructuredData
	{
		return $this->blogLocalizationStructuredDataFactory->create($this->blogLocalization, $this->mutation);
	}

}
