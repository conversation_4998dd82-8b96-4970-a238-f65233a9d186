<?php declare(strict_types = 1);

namespace App\PostType\Modal\AdminModule\Components\Form;

use App\AdminModule\Components\PostType\Localizations\Localizations;
use App\AdminModule\Components\PostType\Localizations\LocalizationsFactory;
use App\Model\WasteDump\WasteDump;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\PostType\Modal\AdminModule\Components\Form\FormData\BaseFormData;
use ArrayIterator;
use Closure;
use Nette\Application\UI\Control;
use Nette\Http\IRequest;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use App\Model\ConfigService;
use App\Model\CustomField\SuggestUrls;
use App\Model\Link\LinkFactory;
use App\Model\Translator;
use App\Model\TranslatorDB;
use Nextras\Orm\Entity\IEntity;

class Form extends Control
{

	/** @var ICollection|Mutation[] */
	private ICollection $mutations;

	/** @var array|mixed|null */
	private mixed $postData;

	public function __construct(
		private readonly WasteDump $wasteDump,
		private readonly ModalLocalization $modalLocalization,
		private readonly User $userEntity,
		private readonly SuggestUrls $urls,
		private readonly Translator $translator,
		private readonly TranslatorDB $translatorDB,
		private readonly Builder $formBuilder,
		private readonly Handler $formHandler,
		private readonly Orm $orm,
		private readonly ConfigService $configService,
		private readonly LocalizationsFactory $languageFactory,
	)
	{
		$this->onAnchor[] = Closure::fromCallable([$this, 'init']);
	}


	private function init(): void
	{
		$this->mutations = $this->orm->mutation->findAll();
		$method = $this->getPresenter()->request->getMethod();
		if ($method === IRequest::POST) {
			$this->postData = (array) $this->getPresenter()->request->getPost();
		} else {
			$this->postData = [];
		}
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);
		$template->add('RS_TEMPLATE_DIR', RS_TEMPLATE_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);

		$template->parent = $this->modalLocalization->getParent();
		$template->entityLocalization = $this->modalLocalization;
		$template->mutation = $this->modalLocalization->mutation;
		$template->orm = $this->orm;
		$template->translatorDB = $this->translatorDB;
		$template->fileUploadLink = $this->presenter->link(':Admin:File:upload');

		$template->otherMutations = $this->modalLocalization->modal->localizations->toCollection()->findBy(['mutation!=' => $this->modalLocalization->mutation]);
		$activeMutationLangCodes = [];
		foreach ($this->modalLocalization->modal->localizations as $localization) {
			$activeMutationLangCodes[] = $localization->mutation->langCode;
		}

		$template->missingMutations = $this->mutations->findBy(['langCode!=' => $activeMutationLangCodes]);

		$template->config = $this->configService->getParams();
		$template->userEntity = $this->userEntity;

		$template->urls = $this->urls;
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$from = new \Nette\Application\UI\Form();
		$from->setMappedType(BaseFormData::class);

		$this->formBuilder->build($from, $this->modalLocalization);

		$from->setTranslator($this->translator);
		$from->onSuccess[] = [$this, 'formSucceeded'];
		$from->onError[] = [$this, 'formError'];

		return $from;
	}


	public function formError(\Nette\Application\UI\Form $form): void
	{
		bd($form->errors);
		$this->flashMessage('Error', 'error');
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, BaseFormData $data): void
	{
		$this->formHandler->handle($this->modalLocalization, $this->userEntity, $data, ArrayHash::from($this->postData));

		$this->presenter->redirect('edit', ['id' => $this->modalLocalization->id]);
	}


	public function handleDelete(): void
	{
		$entity = $this->modalLocalization;
		assert($entity instanceof IEntity);
		$this->wasteDump->throwAway($entity, $this->userEntity, $this->modalLocalization->getParent()->getInternalName() . ' - ' . $this->modalLocalization->getName() . ' - ' . $entity->getMutation()->langCode);
		$this->presenter->redirect('default');
	}


	protected function createComponentLanguage(): Localizations
	{
		return $this->languageFactory->create(
			localizationEntity: $this->modalLocalization,
		);
	}

}
