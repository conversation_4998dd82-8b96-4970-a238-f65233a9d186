cf:
	fields:
		countries:
			type: list
			label: "Země"
			items:
				country:
					type: suggest
					label: "Země"
					url: @cf.suggestUrls.searchCountry
					subType: state
					multiple: true
		title:
			type: text
			label: "Titulek"
		content:
			type: textarea
			label: "Obsah"
		link:
			@cf.definitions.linkChoose

	templates:
		"modal": [@cf.intro_media, @cf.colorTheme, @cf.title, @cf.content, @cf.link]


application:
	mapping:
		Modal: App\PostType\Modal\*Module\Presenters\*Presenter

parameters:
	postTypeRoutes:
		Modal: modal
	config:
		modal:
			paging: 9
services:
	## MODAL

	- App\PostType\Modal\AdminModule\Components\Form\Builder
	- App\PostType\Modal\AdminModule\Components\Form\Handler
	- App\PostType\Modal\Model\ModalLocalizationFacade
	- App\PostType\Modal\AdminModule\Components\Form\FormFactory
