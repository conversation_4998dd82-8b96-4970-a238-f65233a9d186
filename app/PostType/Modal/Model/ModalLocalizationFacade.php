<?php declare(strict_types = 1);

namespace App\PostType\Modal\Model;


use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\PostType\Core\Model\EntityLocalizationFacade;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Modal\Model\Orm\Modal;
use App\PostType\Modal\Model\Orm\ModalLocalization;

class ModalLocalizationFacade implements EntityLocalizationFacade
{

	public function __construct(
		private Orm $orm,
	)
	{
	}


	public function create(Mutation $mutation, ParentEntity|null $localizableEntity): LocalizationEntity
	{
		$localization = new ModalLocalization();
		$this->orm->modalLocalization->attach($localization);
		$localization->mutation = $mutation;

		if ($localizableEntity === null) {
			$localizableEntity = new Modal();
			$localization->modal = $localizableEntity;
		} else {
			assert($localizableEntity instanceof Modal);
			$localization->modal = $localizableEntity;
		}

		$this->orm->persistAndFlush($localization);

		return $localization;
	}


	public function remove(LocalizationEntity $localizableEntity): void
	{
		assert($localizableEntity instanceof ModalLocalization);

		$parent = $localizableEntity->getParent();
		$this->orm->modalLocalization->remove($localizableEntity);

		if ($parent->localizations->count() === 0) {
			$this->orm->modal->remove($parent);
		}

		$this->orm->flush();
	}

}
