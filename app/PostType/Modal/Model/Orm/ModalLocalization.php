<?php

declare(strict_types=1);

namespace App\PostType\Modal\Model\Orm;

use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\RoutableEntity;
use App\Model\Orm\Traits\HasCustomContent;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Core\Model\Hidable;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Core\Model\ParentEntity;
use App\PostType\Core\Model\Publishable;
use App\PostType\Core\Model\Validatable;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;

/**
 * @property int $id {primary}
 * @property string $name {default ''}
 * @property bool $public {default false}
 * @property bool $hide {default false}
 * @property bool $isMain {default false}
 * @property DateTimeImmutable|null $publicFrom {default now}
 * @property DateTimeImmutable|null $publicTo {default '+100 year'}
 * @property DateTimeImmutable|null $editedTime
 * @property int|null $edited
 *
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property ArrayHash $customContentJson {container JsonContainer}
 *
 * RELATIONS
 * @property Modal $modal {M:1 Modal::$localizations}
 * @property Mutation $mutation {m:1 Mutation, oneSided=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 * @property ArrayHash|null $cc {virtual}
 * @property-read string $template {virtual}
 */
class ModalLocalization extends RoutableEntity implements LocalizationEntity, Publishable, Validatable, Hidable
{

	use HasCustomFields;
	use HasCustomContent;


	protected function getterTemplate(): string
	{
		return ':Modal:Admin:Modal:default';
	}


	protected function getterPath(): array
	{
		return [];
	}



	public function getId(): int
	{
		return $this->id;
	}

	public function getMutation(): Mutation
	{
		return $this->mutation;
	}


	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	public function getParent(): Modal
	{
		return $this->modal;
	}


	public function setParent(ParentEntity $parentEntity): void
	{
		assert($parentEntity instanceof Modal);
		$this->modal = $parentEntity;
	}


	public function getIsPublic(): bool
	{
		return $this->public;
	}

	public function setIsPublic(bool $isPublic): void
	{
		$this->public = $isPublic;
	}

	public function getPublicFrom(): DateTimeImmutable|null
	{
		return $this->publicFrom;
	}

	public function getPublicTo(): DateTimeImmutable|null
	{
		return $this->publicTo;
	}

	public function setPublicFrom(?DateTimeImmutable $publicFrom): void
	{
		$this->publicFrom = $publicFrom;
	}

	public function setPublicTo(?DateTimeImmutable $publicTo): void
	{
		$this->publicTo = $publicTo;
	}

	public function getName(): string
	{
		return $this->name;
	}

	public function setName(string $name): void
	{
		$this->name = $name;
	}


	public function getHide(): bool
	{
		return $this->hide;
	}

	public function setHide(bool $hide): void
	{
		$this->hide = $hide;
	}

}
