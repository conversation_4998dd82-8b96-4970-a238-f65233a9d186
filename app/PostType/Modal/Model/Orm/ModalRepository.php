<?php declare(strict_types = 1);

namespace App\PostType\Modal\Model\Orm;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Modal getById($id)
 * @method Modal[]|ICollection searchByName(string $q, array $excluded)
 * @method Modal[]|ICollection findByExactOrder(array $ids)
 */
final class ModalRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [Modal::class];
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
