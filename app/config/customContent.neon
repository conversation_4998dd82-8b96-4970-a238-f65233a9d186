extensions:
	cc: App\Model\CustomContent\CustomContentExtension


cc:
	definitions:
		text:
			type: group
			label: "Text"
			translatable: true
			items:
				text: @cf.definitions.text
		content_icons:
			type: group
			label: "Content with icon"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image
							label: "Obrázek / ikona (150x150)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "<PERSON><PERSON>ah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		offices:
			type: group
			label: "Offices"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				location:
					type: checkbox
					label: "Filtrace všech Pobočky podle lokace"
				items:
					type: list
					label: "Pobočky"
					items:
						store: @cf.definitions.store
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		downloads:
			type: group
			label: "Downloads"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Soubory"
					items:
						name:
							type: text
							label: "Název souboru"
							translatable: true
						image:
							type: image
							label: "Obrázek (416x384)"
						file:
							type: file
							label: "Soubor"
						external:
							type: text
							label: "Externí odkaz (nezapomeňte na https://) (volitelné)"
							placeholder: "Zadejte pouze v případě cloudového uložiště (Dropbox, GoogleDisk apod.)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		downloads_files:
			type: group
			label: "Downloads (tiles)"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Soubory"
					items:
						name:
							type: text
							label: "Název souboru"
							translatable: true
						image:
							type: image
							label: "Obrázek (664X392)"
						file:
							type: file
							label: "Soubor"
						external:
							type: text
							label: "Externí odkaz (nezapomeňte na https://) (volitelné)"
							placeholder: "Zadejte pouze v případě cloudového uložiště (Dropbox, GoogleDisk apod.)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		annotation:
			type: group
			label: "Annotation"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				text:
					type: textarea
					label: "Anotace" # todo remove me
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		dont_forget:
			type: group
			label: "Don't forget"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				variant:
					type: select
					label: "Barevná varianta"
					defaultValue: 'gray'
					options: [
						{ label: "Šedá", value: "gray" },
						{ label: "Zlatá", value: "gold" },
					]
				content:
					type: tinymce
					label: "Obsah"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		citation:
			type: group
			label: "Citation"
			items:
				text:
					type: textarea
					label: "Vyjádření"
					translatable: true
				author:
					type: text
					label: "Autor"
				position:
					type: text
					label: "Pozice"
				bg:
					type: checkbox
					label: "Varianta s pozadím"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		content:
			type: group
			label: "Content"
			items:
				content:
					type: tinymce
					label: "Obsah" # todo remove me
					translatable: true
				two_columns:
					type: checkbox
					label: "Dvousloupcový"
				align:
					type: select
					label: "Zúžení a zarovnání"
					options: [ {
						label: "Doleva"
						value: "left"
					}, {
						label: "Na střed"
						value: "center"
					}, {
						label: "Doprava"
						value: "right"
					}]
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		content_block:
			type: group
			label: "Content with count"
			items:
				highlight:
					type: text
					label: "Zvýrazněný text"
					translatable: true
				desc:
					type: text
					label: "Popisek"
					translatable: true
				content:
					type: tinymce
					label: "Obsah"
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		content_photo:
			type: group
			label: "Content with photo"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				content:
					type: tinymce
					label: "Obsah"
					translatable: true
				inverted:
					type: checkbox
					label: "Obrázek napravo"
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (1880x1400)"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		photo:
			type: group
			label: "Photo"
			items:
				src:
					type: image
					label: "Vložení obrázku (928x584)"
				text:
					type: text
					label: "Popis obrázku"
					translatable: true
				inverted:
					type: checkbox
					label: "Pozadí napravo"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		photos:
			type: group
			label: "Photos with description"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image
							label: "Obrázek (448x352)"
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		workflow:
			type: group
			label: "Workflow"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						text:
							type: text
							label: "Text"
							translatable: true
				imgLeft:
					type: image
					label: "Obrázek vlevo (průhledné png, 480x1000)"
				imgRight:
					type: image
					label: "Obrázek vpravo (průhledné png, 480x1000)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		description_list:
			type: group
			label: "Text blocks"
			items:
				hiddenTitle:
					type: text
					label: "Skrytý nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						text:
							type: textarea
							label: "Text"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		timeline:
			type: group
			label: "Timeline"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						time:
							type: text
							label: "Časový úsek"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		materials:
			type: group
			label: "Materials"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				annot:
					type: textarea
					label: "Anotace"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image
							label: "Obrázek (208x208)"
						link:
							extends: @cf.definitions.linkChoose
							label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		numbers:
			extends: @cf.definitions.numbers
			type: group
			label: "Numbers"
		numbers_bg:
			type: group
			label: "Numbers with background"
			items:
				bg:
					extends: @cf.definitions.imgs
					label: "Pozadí (3840x3840)"
				items:
					extends: @cf.definitions.numbers
					type: list
					label: "Čísla"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		photo_content:
			type: group
			label: "Horizontal photo with content"
			items:
				content:
					type: tinymce
					label: "Obsah"
					translatable: true
				inverted:
					type: checkbox
					label: "Obrázek napravo"
				variant:
					type: select
					label: "Varianta pozadí za fotografií"
					options: [ {
						label: "Nahoře"
						value: "top"
					}, {
						label: "Dole"
						value: "bottom"
					}]
					defaultValue: "top"
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (840x840)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		photos_half:
			type: group
			label: "Two photos"
			items:
				image1:
					extends: @cf.definitions.image1
				image2:
					extends: @cf.definitions.image1
					label: "Obrázek 2"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		app:
			type: group
			label: "Application"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				content:
					type: textarea
					label: "Obsah"
					translatable: true
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (342x507)"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		photo_carousel:
			type: group
			label: "Photo carousel"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Obrázky"
					items:
						image:
							extends: @cf.definitions.contentPhotoImage
							label: "Obrázek (3840x3840)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		others:
			type: group
			label: "Others"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						page:
							type: suggest
							subType: tree
							label: "Odkaz v rámci webu"
							url: @cf.suggestUrls.searchMutationPage

						external:
							type: text
							label: "Odkaz (volitelné, nezapomeňte na https://)"
							placeholder: "https://..."

						image:
							type: image
							label: "Obrázek (996x606)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		technical_parameters:
			type: group
			label: "Technical parameters"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (824x600)"
				items:
					type: list
					label: "Položky"
					items:
						name:
							type: text
							label: "Název skupiny parametrů"
							translatable: true
						items:
							type: list
							label: "Parametry"
							items:
								name:
									type: text
									label: "Název parametru"
									translatable: true
								value:
									type: text
									label: "Hodnota parametru"
									translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		color_carousel:
			type: group
			label: "Color carousel"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Karusely"
					items:
						color_name:
							type: text
							label: "Název barvy"
							translatable: true
						color:
							type: text
							label: "Hexadecimální kód barvy tlačítka"
							placeholder: "#bdbdbd"
						images:
							type: image
							label: "Obrázky (840x840)"
							multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		designers:
			type: group
			label: "Designers"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				systemDesigners:
					type: checkbox
					label: "Načítat systémové designery (pouze produkty a reference)"
				items:
					type: list
					label: "Lidé"
					items:
						person:
							type: suggest
							label: "Výběr designéra"
							subType: designer
							url: @cf.suggestUrls.searchDesignersInMutation
						title:
							type: text
							label: "Nadpis"
							translatable: true
						annot:
							type: textarea
							label: "Anotace (kurzíva)"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		reference:
			type: group
			label: "Reference"
			items:
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (928x844)"
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				annot:
					type: textarea
					label: "Anotace"
					translatable: true
				name:
					type: text
					label: "Jméno a příjmení"
				position:
					type: text
					label: "Pozice"
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		installation_details:
			type: group
			label: "Installation details"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				image:
					type: group
					label: "Obrázek"
					items:
						src:
							type: image
							label: "Obrázek (1920x1920)"
						alt:
							type: text
							label: "Alternativní popis"
							translatable: true
						vertical:
							type: select
							label: "Vertikální zarovnání"
							options: [ {
								label: "Nahoru"
								value: "top"
							}, {
								label: "Na střed"
								value: "center"
							}, {
								label: "Dolu"
								value: "bottom"
							}]
							defaultValue: "center"
						horizontal:
							type: select
							label: "Horizontální zarovnání"
							options: [ {
								label: "Doleva"
								value: "left"

							}, {
								label: "Na střed"
								value: "center"
							}, {
								label: "Doprava"
								value: "right"
							}]
							defaultValue: "center"
				items:
					type: list
					label: "Části"
					items:
						name:
							type: text
							label: "Název části"
							translatable: true
						content:
							type: textarea
							label: "Popis"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		carousel_bg:
			type: group
			label: "Carousel with background"
			items:
				subtitle:
					type: text
					label: "Podnapids"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				isThin:
					type: checkbox
					label: "Nízká varianta"
				items:
					type: list
					label: "Položky"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: tinymce
							label: "Obsah"
							translatable: true
						bg:
							type: image
							label: "Obrázek na pozadí (3840x3840)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		sections:
			type: group
			label: "Sections (image and text)"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Sekce"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						subtitle:
							type: text
							label: "Podnadpis"
							translatable: true
						image:
							type: image
							label: "Obrázek (1200x728)"
						content:
							type: textarea
							label: "Obsah"
							translatable: true
						icons:
							type: list
							label: "Ikony"
							items:
								icon:
									type: image
									label: "Ikona (svg nebo 128x128)"
								text:
									type: text
									label: "Popis"
									translatable: true
						variant:
							type: select
							label: "Varianta"
							options: [ {
								label: "Varianta 1 (zlatá)"
								value: "v1"
							}, {
								label: "Varianta 2 (rámeček, foto nahoru)"
								value: "v2"
							}, {
								label: "Varianta 3 (šedá)"
								value: "v3"
							}, {
								label: "Varianta 4 (rámeček, foto dolu)"
								value: "v4"
							}, {
								label: "Varianta 5 (šedá, foto nahoru)"
								value: "v5"
							}]
							defaultValue: "v1"
						btn:
							extends: @cf.definitions.linkChoose
							label: "Odkaz"
						spacing:
							@cf.definitions.spacing
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		contact_form_person:
			type: group
			label: "Contact form with person"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				locationBased:
					type: checkbox
					label: "Přepínač dle lokace"
				person:
					type: suggest
					label: "Výběr kontaktní osoby"
					subType: person
					url: @cf.suggestUrls.searchMutationPerson
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		contact_form:
			type: group
			label: "Contact form"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		banner:
			type: group
			label: "Banner"
			items:
				text:
					type: text
					label: "Text"
					translatable: true
				bg:
					extends: @cf.definitions.imgs
					label: "Pozadí (3840x3840)"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		share:
			type: group
			label: "Share"
			items:
				text:
					type: text
					label: "Text"
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		composition_options:
			type: group
			label: "Composition options"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Možnosti"
					items:
						image:
							type: image
							label: "Obrázek (150x150)"
						text:
							type: text
							label: "Název"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		accordion:
			type: group
			label: "Accordion"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				image:
					type: image
					label: "Obrázek (736x644)"
				items:
					type: list
					label: "Položky"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
						isOpen:
							type: checkbox
							label: "Rozevřený"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		gallery:
			type: group
			label: "Gallery"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				images:
					type: image
					label: "Obrázky (1200x1200)"
					multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		highlights:
			type: group
			label: "Highlights"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image
							label: "Obrázek (270x270)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		btn:
			type: group
			label: "Button"
			items:
				btn:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		grid_gallery:
			type: group
			label: "Grid gallery"
			items:
				side:
					type: group
					label: "Obsah vedle galerie (volitelné)"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						subtitle:
							type: text
							label: "Podnadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
						reversed:
							type: checkbox
							label: "Umístění na opačné straně"
				variant:
					type: select
					label: "Varianta rozvržení galerie"
					options: [ {
						label: "2 obrázky / 1 text"
						value: "2-1"
					}, {
						label: "3 obrázky / 1 text"
						value: "3-1"
					}, {
						label: "2 obrázky / 2 texty"
						value: "2-2"
					}, {
						label: "2 obrázky / 3 texty"
						value: "2-3"
					}, {
						label: "3 obrázky / 3 texty"
						value: "3-3"
					}, {
						label: "4 obrázky / 3 texty"
						value: "4-3"
					}, {
						label: "5 obrázků / 3 texty"
						value: "5-3"
					}]
					defaultValue: "5-3"
				gallery:
					type: group
					label: "Položky galerie"
					items:
						images:
							type: image
							label: "Obrázky (1200x1200)"
							multiple: true
						texts:
							type: list
							label: "Popisky"
							items:
								text:
									type: textarea
									label: "Popisek"
									translatable: true
				btnGallery:
					type: checkbox
					label: "Přidat tlačítko vedoucí do galerie"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		img_full:
			type: group
			label: "Image (full width)"
			items:
				image:
					type: image
					label: "Obrázek (1920x816)"
				alt:
					type: text
					label: "Alternativní text"
					translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		video:
			type: group
			label: "Video"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				link:
					type: text
					label: "Odkaz na youtube / vimeo"
				file:
					type: file
					label: "Videosoubor (mp4)"
				content:
					type: tinymce
					label: "Obsah"
					translatable: true
				full:
					type: checkbox
					label: "Zobrazení přes celou šířku"
				autoplay:
					type: checkbox
					label: "Automatické přehrávání"
				poster:
					type: image
					label: "Zástupný obrázek videa (1920x1080)"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		videos:
			type: group
			label: "Videogallery"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						poster:
							type: image
							label: "Zástupný obrázek videa (1200x675)" # todo rozmery
						link:
							type: text
							label: "Odkaz na Youtube / Vimeo"
						file:
							type: file
							label: "Videosoubor (mp4)"
						autoplay:
							type: checkbox
							label: "Automatické přehrávání"
						content:
							type: textarea
							label: "Popis"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		carousel:
			type: group
			label: "Photo carousel with content"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				content_title:
					type: text
					label: "Nadpis obsahu"
					translatable: true
				content:
					type: textarea
					label: "Obsah"
					translatable: true
				images:
					type: image
					label: "Obrázky (1200x1200)"
					multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		carousel_designers:
			type: group
			label: "Designers carousel"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Lidé"
					items:
						person:
							type: suggest
							label: "Výběr designéra"
							subType: designer
							url: @cf.suggestUrls.searchDesignersInMutation
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		pressList:
			type: group
			label: "Press list"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Press"
					items:
						press:
							type: suggest
							label: "Výběr press"
							subType: press
							url: @cf.suggestUrls.searchMutationPress
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		modalList:
			type: group
			label: "Modal list"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				text:
					type: textarea
					label: "Text"
					translatable: true
				link:
					@cf.definitions.linkChoose
				spacing:
					@cf.definitions.spacing
		personList:
			type: group
			label: "Persons"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Lidé"
					items:
						person:
							type: suggest
							label: "Výběr lidí"
							subType: person
							url: @cf.suggestUrls.searchMutationPerson
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		social:
			type: group
			label: "Socials"
			items:
				mail:
					type: text
					label: "E-mail"
				fb:
					type: text
					label: "Facebook"
				ig:
					type: text
					label: "Instagram"
				pint:
					type: text
					label: "Pinterest"
				yt:
					type: text
					label: "Youtube"
				li:
					type: text
					label: "Linkedin"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		embed:
			type: group
			label: "Embed"
			items:
				url:
					type: text
					label: "URL adresa"
				spacing:
					@cf.definitions.spacing

		images:
			type: group
			label: "Photogallery"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				images:
					type: image
					label: "Obrázky"
					multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		pages:
			type: group
			label: "Pages"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				annot:
					type: textarea
					label: "Anotace"
					translatable: true
				bg:
					type: checkbox
					label: "Jednobarevné pozadí"
				fourColumns:
					type: checkbox
					label: "Čtyři sloupce"
				highlightFirst:
					type: checkbox
					label: "Zvýraznit první položku"
				items:
					type: list
					label: "Položky"
					items:
						page:
							type: suggest
							subType: tree
							label: "Odkaz v rámci webu"
							url: @cf.suggestUrls.searchMutationPage
						image:
							type: image
							label: "Obrázek (840x840)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						external:
							type: text
							label: "Odkaz (volitelné, nezapomeňte na https://)"
							placeholder: "https://..."
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		design_info:
			type: group
			label: "Design info"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				annot:
					type: textarea
					label: "Anotace"
					translatable: true
				image:
					extends: @cf.definitions.contentPhotoImage
					label: "Obrázek (1920x1152)"
				variant:
					type: select
					label: "Varianta pozadí"
					options: [ {
						label: "Nahoře"
						value: "bg-top"
					}, {
						label: "Dole"
						value: "bg-down"
					}]
					defaultValue: "bg-top"
				items:
					type: list
					label: "Položky"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		design_steps:
			type: group
			label: "Design steps"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Sekce"
					items:
						image:
							type: image
							label: "Obrázek (567x400)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Popis"
							translatable: true
						preview:
							type: list
							label: "Náhledy"
							items:
								image:
									type: image
									label: "Obrázek (volitelné, 180x180)"
								link:
									extends: @cf.definitions.linkChoose
									label: "Odkaz"
						carousels:
							type: list
							label: "Foto karusely"
							items:
								image:
									extends: @cf.definitions.contentPhotoImage
									label: "Obrázek (180x180)"
								images:
									type: image
									label: "Obrázky v karuselu (448x336)"
									multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		composition_examples:
			type: group
			label: "Composition examples"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				annot:
					type: textarea
					label: "Anotace"
					translatable: true
				items:
					type: list
					label: "Sekce"
					items:
						icon:
							type: image
							label: "Obrázek / ikona (180x180)"
						label:
							type: text
							label: "Název sekce"
							translatable: true
						images:
							type: image
							label: "Obrázky (1200x1200)"
							multiple: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		cta:
			type: group
			label: "CTA"
			items:
				text:
					type: text
					label: "Text"
					translatable: true
				link:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		lighting:
			type: group
			label: "Lighting"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				contents:
					type: list
					label: "Popis"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						content:
							type: textarea
							label: "Obsah"
							translatable: true
				imageLight:
					type: image
					label: "Obrázek 1 (1200x728)"
				imageDark:
					type: image
					label: "Obrázek 2 (1200x728)"
				image3:
					type: image
					label: "Obrázek 3 (1200x728)"
				image4:
					type: image
					label: "Obrázek 4 (1200x728)"

				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		story:
			type: group
			label: "Story"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				items:
					type: list
					label: "Záložky"
					items:
						name:
							type: text
							label: "Název záložky"
							translatable: true
						items:
							type: list
							label: "Bloky s obsahem"
							items:
								title:
									type: text
									label: "Nadpis"
									translatable: true
								image:
									type: image
									label: "Obrázek (1200x1200)"
								content:
									type: textarea
									label: "Obsah"
									translatable: true
								colspan:
									type: checkbox
									label: "Roztáhnout přes 2 sloupce"
								rowspan:
									type: checkbox
									label: "Roztáhnout přes 2 řádky"
								reverse:
									type: checkbox
									label: "Převrácené"
								link:
									extends: @cf.definitions.linkChoose
									label: "Odkaz"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		references:
			type: group
			label: "References"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						reference:
							type: suggest
							label: "Výběr reference"
							subType: reference
							url: @cf.suggestUrls.searchMutationReference
				btn:
					extends: @cf.definitions.linkChoose
					label: "Odkaz"
				newest:
					type: checkbox
					label: "Nejnovější reference"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		promo:
			type: group
			label: "Promotion of event"
			items:
				title:
					type: text
					label: "Nadpis (nepovinné)"
					translatable: true
				content:
					type: textarea
					label: "Obsah"
					translatable: true
				event:
					type: suggest
					subType: event
					label: "Událost"
					url: @cf.suggestUrls.searchEventInMutation

				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		promos:
			type: group
			label: "Promos"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				items:
					type: list
					label: "Položky"
					items:
						image:
							type: image
							label: "Obrázek (928x844)"
						city:
							type: text
							label: "Město (dodatečný text)"
							translatable: true
						link:
							extends: @cf.definitions.linkChoose
							label: "Odkaz"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		referenceContent:
			type: group
			label: "Reference with content"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				content:
					type: textarea
					label: "Obsah"
					translatable: true
				reference:
					type: suggest
					label: "Výběr reference"
					subType: reference
					url: @cf.suggestUrls.searchMutationReference
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		store:
			type: group
			label: "Store"
			items:
				store:
					type: suggest
					label: "Výběr pobočky"
					subType: contact
					url: @cf.suggestUrls.searchMutationContact
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		stores:
			type: group
			label: "Stores"
			items:
				items:
					type: list
					label: "Položky"
					items:
						title:
							type: text
							label: "Nadpis"
							translatable: true
						store:
							@cf.definitions.store
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		news:
			type: group
			label: "News"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				articles:
					type: group
					label: "Články"
					items:
						newest:
							type: checkbox
							label: "Nejnovější novinky"
						items:
							type: list
							label: "Články"
							items:
								page:
									type: suggest
									label: "Výběr článku"
									subType: blog
									url: @cf.suggestUrls.searchBlogsInMutation
				categories:
					type: checkbox
					label: "Zobrazit kategorie"
				bg:
					type: checkbox
					label: "Pozadí"
				btn:
					extends: @cf.definitions.linkChoose
					label: "Tlačítko"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName
		location:
			type: group
			label: "Location map"
			items:
				title:
					type: text
					label: "Nadpis"
					translatable: true
				titleCentered:
					type: checkbox
					label: "Vycentrovat nadpis"
				filter:
					type: checkbox
					label: "Zobrazit filtr"
				allContacts:
					type: checkbox
					label: "Vypsat všechny kontakty"
				showCustomMarker:
					type: checkbox
					label: "Zobrazit vlastní bod na mapě"
				customMarker:
					type: group
					label: "Vlastní bod na mapě"
					items:
						img:
							type: image
							label: "Obrázek (560x374)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						officeTitle:
							type: text
							label: "Podnadpis"
							translatable: true
						link:
							type: text
							label: "Odkaz"
						lat:
							type: text
							label: "Zeměpisná šířka (latitude)"
						lng:
							type: text
							label: "Zeměpisná délka (longitude)"
						info:
							type: textarea
							label: "Informace o události (datum apod.)"
						address:
							type: text
							label: "Adresa"
						tel:
							type: text
							label: "Telefon"
						telInfo:
							type: text
							label: "Info k telefonu"
							translatable: true
						mail:
							type: text
							label: "E-mail"
						map_link:
							type: text
							label: "Odkaz na Google Mapu"
				items:
					type: list
					label: "Pobočky"
					items:
						store:
							type: suggest
							label: "Výběr poboček"
							subType: contact
							url: @cf.suggestUrls.searchMutationContact
				variant:
					type: select
					label: "Varianta" # todo remove me
					options: [ {
						label: "Bílá"
						value: "white"
					}, {
						label: "Šedá (defaultní)"
						value: "default"
					}, {
						label: "Černá"
						value: "black"
					}]
					defaultValue: "default"
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

	components:
		annotation:
			icon: "font"
			template: "annotation"
			definition: @cc.definitions.annotation
			# hotkey: true
			category: "Obsah"
		dontForget:
			icon: "highlighter"
			template: "dont_forget"
			definition: @cc.definitions.dont_forget
			category: "Obsah"
		contentPhoto:
			icon: "columns"
			template: "content_photo"
			definition: @cc.definitions.content_photo
			category: "Obsah"
		citation:
			icon: "quote-right"
			template: "citation"
			definition: @cc.definitions.citation
			category: "Obsah"
		content:
			icon: "align-left"
			template: "content"
			definition: @cc.definitions.content
			category: "Obsah"
		contentBlock:
			icon: "border-all"
			template: "content_block"
			definition: @cc.definitions.content_block
			category: "Obsah"
		photo:
			icon: "image"
			template: "photo"
			definition: @cc.definitions.photo
			category: "Galerie"
		downloads:
			icon: "file"
			template: "downloads"
			definition: @cc.definitions.downloads
			category: "Rozcestníky"
		downloadsFiles:
			icon: "file"
			template: "downloads_files"
			definition: @cc.definitions.downloads_files
			category: "Rozcestníky"
		contentIcons:
			icon: "icons"
			template: "content_icons"
			definition: @cc.definitions.content_icons
			category: "Obsah"
		offices:
			icon: "house-user"
			template: "offices"
			definition: @cc.definitions.offices
			category: "Pobočky"
		photos:
			icon: "grip-horizontal"
			template: "photos"
			definition: @cc.definitions.photos
			category: "Galerie"
		workflow:
			icon: "list-ol"
			template: "workflow"
			definition: @cc.definitions.workflow
		descriptionList:
			icon: "grip-horizontal"
			template: "description_list"
			definition: @cc.definitions.description_list
			category: "Obsah"
		timeline:
			icon: "clock"
			template: "timeline"
			definition: @cc.definitions.timeline
		materials:
			icon: "lightbulb"
			template: "materials"
			definition: @cc.definitions.materials
			# category: "Obsah"
		numbers:
			icon: "grip-horizontal"
			template: "numbers"
			definition: @cc.definitions.numbers
		numbersBg:
			icon: "grip-horizontal"
			template: "numbers_bg"
			definition: @cc.definitions.numbers_bg
		photoContent:
			icon: "columns"
			template: "photo_content"
			definition: @cc.definitions.photo_content
			category: "Obsah"
		photosHalf:
			icon: "columns"
			template: "photos_half"
			definition: @cc.definitions.photos_half
			category: "Galerie"
		app:
			icon: "app-store-ios"
			template: "app"
			definition: @cc.definitions.app
		photoCarousel:
			icon: "images"
			template: "photo_carousel"
			definition: @cc.definitions.photo_carousel
			category: "Galerie"
		others:
			icon: "map-signs"
			template: "others"
			definition: @cc.definitions.others
			category: "Rozcestníky"
		pages:
			icon: "map-signs"
			template: "pages"
			definition: @cc.definitions.pages
			category: "Rozcestníky"
		technicalParameters:
			icon: "tools"
			template: "technical_parameters"
			definition: @cc.definitions.technical_parameters
		colorCarousel:
			icon: "tint"
			template: "color_carousel"
			definition: @cc.definitions.color_carousel
			category: "Galerie"
		designers:
			icon: "user-alt"
			template: "designers"
			definition: @cc.definitions.designers
			category: "Lidé"
		pressList:
			icon: "user-alt"
			template: "presses"
			definition: @cc.definitions.pressList
			category: "Rozcestníky"
		modalList:
			icon: "user-alt"
			template: "modales"
			definition: @cc.definitions.modalList
			category: "Obsah"
		personList:
			icon: "user-friends"
			template: "people"
			definition: @cc.definitions.personList
			category: "Lidé"
		reference:
			icon: "highlighter"
			template: "reference"
			definition: @cc.definitions.reference
			category: "Lidé"
		installationDetails:
			icon: "lightbulb"
			template: "installation_details"
			definition: @cc.definitions.installation_details
		carouselBg:
			icon: "images"
			template: "carousel_bg"
			definition: @cc.definitions.carousel_bg
			category: "Galerie"
		sections:
			icon: "columns"
			template: "sections"
			definition: @cc.definitions.sections
			category: "Obsah"
		contactFormPerson:
			icon: "phone-alt"
			template: "contact_form_person"
			definition: @cc.definitions.contact_form_person
			category: "Pobočky"
		contactForm:
			icon: "phone-alt"
			template: "contact_form"
			definition: @cc.definitions.contact_form
			category: "Pobočky"
		banner:
			icon: "ad"
			template: "banner"
			definition: @cc.definitions.banner
			category: "Odkazy"
		share:
			icon: "share-alt"
			template: "share"
			definition: @cc.definitions.share
			category: "Odkazy"
		compositionOptions:
			icon: "deezer"
			template: "composition_options"
			definition: @cc.definitions.composition_options
		embed:
			icon: "glasses"
			template: "embed"
			definition: @cc.definitions.embed
		accordion:
			icon: "buffer"
			template: "accordion"
			definition: @cc.definitions.accordion
			category: "Obsah"
		gallery:
			icon: "images"
			template: "gallery"
			definition: @cc.definitions.gallery
			category: "Galerie"
		highlights:
			icon: "grip-vertical"
			template: "highlights"
			definition: @cc.definitions.highlights
			category: "Obsah"
		btn:
			icon: "bootstrap"
			template: "btn"
			definition: @cc.definitions.btn
			category: "Odkazy"
		gridGallery:
			icon: "images"
			template: "grid_gallery"
			definition: @cc.definitions.grid_gallery
			category: "Galerie"
		imgFull:
			icon: "image"
			template: "img_full"
			definition: @cc.definitions.img_full
			category: "Galerie"
		videos:
			icon: "play"
			template: "videos"
			definition: @cc.definitions.videos
			category: "Galerie"
		video:
			icon: "play"
			template: "video"
			definition: @cc.definitions.video
			category: "Galerie"
		carousel:
			icon: "images"
			template: "carousel"
			definition: @cc.definitions.carousel
			category: "Galerie"
		carouselDesigners:
			icon: "user-friends"
			template: "carousel_designers"
			definition: @cc.definitions.carousel_designers
			category: "Lidé"
		social:
			icon: "external-link-alt"
			template: "social"
			definition: @cc.definitions.social
			category: "Odkazy"
		images:
			icon: "images"
			template: "images"
			definition: @cc.definitions.images
			category: "Galerie"
		designInfo:
			icon: "list-ol"
			template: "design_info"
			definition: @cc.definitions.design_info
		designSteps:
			icon: "list-ol"
			template: "design_steps"
			definition: @cc.definitions.design_steps
		composition_examples:
			icon: "list-ol"
			template: "composition_examples"
			definition: @cc.definitions.composition_examples
		cta:
			icon: "bootstrap"
			template: "cta"
			definition: @cc.definitions.cta
			category: "Odkazy"
		lighting:
			icon: "lightbulb"
			template: "lighting"
			definition: @cc.definitions.lighting
		story:
			icon: "book"
			template: "story"
			definition: @cc.definitions.story
			category: "Obsah"
		references:
			icon: "map-signs"
			template: "references"
			definition: @cc.definitions.references
			category: "Rozcestníky"
		promo:
			icon: "map-signs"
			template: "promo"
			definition: @cc.definitions.promo
			category: "Rozcestníky"
		promos:
			icon: "map-signs"
			template: "promos"
			definition: @cc.definitions.promos
			category: "Rozcestníky"
		referenceContent:
			icon: "map-signs"
			template: "reference_content"
			definition: @cc.definitions.referenceContent
			category: "Rozcestníky"
#		products:
#			icon: "product-hunt"
#			template: "products"
#			definition: @cc.definitions.products
#			category: "Rozcestníky"
		store:
			icon: "store-alt"
			template: "store"
			definition: @cc.definitions.store
			category: "Pobočky"
		stores:
			icon: "store-alt"
			template: "stores"
			definition: @cc.definitions.stores
			category: "Pobočky"
		news:
			icon: "map-signs"
			template: "news"
			definition: @cc.definitions.news
			category: "Rozcestníky"
		location:
			icon: "map-marked-alt"
			template: "location"
			definition: @cc.definitions.location
			category: "Pobočky"


	templates:
		":Front:Page:default": *
		":Front:Page:bespoke": *
		":Front:Page:signature": *
		":Front:Page:contact": *
		":Front:Page:download": *
		":Front:Page:about": *
		":Front:Page:cheatsheet3": *
		":Press:Front:Press:default": *

		"authorLocalization": *
		"blogLocalization": *
		"designerLocalization": *
		"referenceLocalization": *
		"contactLocalization": *
		"eventLocalization": *
		"pressLocalization": *
		"press": *
		"Product:detail": *
		":Front:Homepage:default": *

services:
	-
		implement: App\FrontModule\Components\CustomContentRenderer\CustomContentRendererFactory
		inject: true
