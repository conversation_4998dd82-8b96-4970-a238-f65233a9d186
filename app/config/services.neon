	services:

		# Security
		authenticator: App\Model\Security\Authenticator
		acl: App\Model\Security\Acl
		user: App\Model\Security\User

#		odkomentovani: vypne cache
		cacheStorage:
			class: Nette\Caching\Storages\DevNullStorage

#		cacheStorage:
#			class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

		translatorDB:
			class: App\Model\TranslatorDB(%config.translations.insertNew%, %config.translations.markUsage%)


		translator:
			class: App\Model\Translator(%config.adminLang%, %config%)

		configService: App\Model\ConfigService(%config%)

		routerFactory: App\Router\RouterFactory
		router: @routerFactory::createRouter(%config.adminAlias%, %postTypeRoutes%)

		menuService:
			class: App\Model\MenuService


		- App\AdminModule\Presenters\Library\LibraryPresenter

		- App\Model\FileLockFactory

		- \Curl\Curl

		- App\PostType\Page\Model\Orm\TreeModel
		- App\Model\Orm\File\FileModel
		- App\Model\Orm\Alias\AliasModel(adminAlias: %config.adminAlias%)
		- App\Model\Orm\AliasHistory\AliasHistoryModel
		- App\Model\Orm\User\UserModel
		- App\Model\Orm\UserHash\UserHashModel
		- App\Model\Orm\LibraryImage\LibraryImageModel
		- App\Model\Orm\LibraryTree\LibraryTreeModel
		- App\Model\Orm\Product\ProductModel
		- App\Model\Orm\ProductLocalization\ProductLocalizationModel
		- App\Model\Orm\ProductVariant\ProductVariantModel
		- App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationModel
		- App\PostType\Page\Model\Orm\CatalogTreeModel
		- App\Model\Orm\ProductReview\ProductReviewModel
		- App\Model\Orm\Parameter\ParameterModel
		- App\Model\Orm\ParameterValue\ParameterValueModel
		- App\Model\Orm\PriceLevel\PriceLevelModel

		- App\Model\PagesFactory

		- App\Model\Orm\EmailTemplate\EmailTemplateModel
		- App\Model\Email\EmailTemplateFactory
		- App\Model\DbalLog("%appDir%/../nettelog/", "mysql")
		- App\Model\EasyMessages
		- App\Model\Orm\State\StateModel
		- App\Model\Orm\String\StringModel

		- App\Console\Model\Sitemap

		# Router
		- App\Model\Router\Filter
		- App\Model\Router\FilterLang
		- App\Model\Router\FilterSeoLink
		- App\Model\Router\FilterAlias
		- App\Model\Router\FilterCommonParameters
		- App\Model\Router\FilterFilterParams
		- App\Model\Router\FilterVariantId
		- App\Model\Router\FilterRedirect


		- App\Model\Link\LinkFactory
		- App\Model\Link\LinkSeo
		- App\Model\Link\LinkChecker
		- Nette\Http\UrlScript
		- App\Model\Orm\Redirect\RedirectModel
		- App\Model\ImageResizerWrapper
		- App\Model\Orm\Mutation\MutationModel
		- App\Model\Mutation\MutationsHolder
		- App\Model\Mutation\MutationHolder
		- App\Model\Mutation\MutationDetector
		- App\Model\Mutation\BrowserMutationDetector
		- App\Model\Url\UrlChecker
		- App\Model\Sentry\SentryLogger
		- App\Model\Orm\FacadeProvider

		- App\Model\CustomField\LazyValueFactory

		- App\Model\Orm\NewsletterEmail\NewsletterEmailModel
		- App\Model\Orm\OrmCleaner

		- App\Model\Cloner\SiteCloner
		- App\Model\Cloner\MutationCloner
		- App\Model\Cloner\EsIndexCommonCloner
		- App\Model\Cloner\PageCommonCloner
		- App\Model\Cloner\ProductLocalizationCommonCloner
		- App\Model\Cloner\BlogTagLocalizationCommonCloner
		- App\Model\Cloner\ContactLocalizationCommonCloner
		- App\Model\Cloner\AuthorLocalizationCommonCloner
		- App\Model\Cloner\SeoLinkLocalizationCommonCloner
		- App\Model\Cloner\BlogLocalizationCommonCloner
		- App\Model\Cloner\DesignerLocalizationCommonCloner
		- App\Model\Cloner\EventLocalizationCommonCloner
		- App\Model\Cloner\PressLocalizationCommonCloner
		- App\Model\Cloner\PersonLocalizationCommonCloner
		- App\Model\Cloner\ModalLocalizationCommonCloner
		- App\Model\Cloner\ClonerProvider

		- App\Model\Duplicator\ProductDuplicator
		- App\FrontModule\Components\ContactBox\ContactBoxFactory
		- App\FrontModule\Components\Favourite\FavouriteFactory
		- App\Model\ValidLazyValueDataExtractor
		- App\Model\Orm\Favourite\FavouriteModel
		- App\Model\GeoHelper\IpLocation

		# ROBOTS
		- App\Model\Robots\RobotsRows(%robotsTxt.rows%)

#		EMAILY
		- App\Model\Mailer\BaseMailer
		- App\Model\Email\CommonFactory

		- App\Model\Mailkit\MailkitService

		nette.latteFactory:
			setup:
			- addFilter(timeAgoInWords, [App\Infrastructure\Latte\Filters, timeAgoInWords])
			- addFilter(plural, [App\Infrastructure\Latte\Filters, plural])
			- addFilter(parseVideoId, [App\Infrastructure\Latte\Filters, parseVideoId])
			- addFilter(niceDate, [App\Infrastructure\Latte\Filters, niceDate])
			- addFilter(money, [App\Infrastructure\Latte\Filters, formatMoney])
			- addFilter(texy, [App\Infrastructure\Latte\Filters, texy])
			- addFilter(skDate, [App\Infrastructure\Latte\Filters, skDate])
			- addFilter(prepareStrJs, [App\Infrastructure\Latte\Filters, prepareStrJs])
			- addFilter(clear, [App\Infrastructure\Latte\Filters, clear])
			- addFilter(copyright, [App\Infrastructure\Latte\Filters, copyright])
			- addFilter(icon, [App\Infrastructure\Latte\Filters, icon])
			- addFilter(lineExploder, [App\Infrastructure\Latte\Filters, lineExploder])
			- addFilter(exploder, [App\Infrastructure\Latte\Filters, exploder])
			- addFilter(stock, [App\Infrastructure\Latte\Filters, stock])
			- addFilter(phoneFormat, [App\Infrastructure\Latte\Filters, phoneFormat])
			- addFilter(formatNumberPrecision, [App\Infrastructure\Latte\Filters, formatNumberPrecision])
			- addFilter(tables, [App\Infrastructure\Latte\Filters, tables])
			- addFilter(eventDate, [App\Infrastructure\Latte\Filters, eventDate])


		- App\Model\Google\GoogleProviderFactory(
			clientId: %google.oauth.clientId%
			clientSecret: %google.oauth.clientSecret%
		)

		- App\Model\Deepl\DeeplClient(
			authKey: %config.deepl.authKey%
		)

		- App\Model\Mailkit\MailkitClient(
			mailkitApiUrl: %config.mailkit.mailkitApiUrl%
			mailkitId: %config.mailkit.mailkitId%
			mailkitMd5: %config.mailkit.mailkitMd5%
			mailkitUserListId: %config.mailkit.mailkitUserListId%
			mailkitTemplateId: %config.mailkit.mailkitTemplateId%
		)
