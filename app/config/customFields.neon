extensions:
	cf: App\Model\CustomField\CustomFieldsExtension

cf:
	definitions:
		content:
			type: tinymce
			label: "<PERSON>bsah"
			translatable: true
		annotation:
			type: textarea
			label: "Anotace"
			translatable: true
		text:
			type: text
			label: "Text"
			translatable: true
		photo:
			type: image
			label: "Fotka"
		photos:
			type: image
			label: "Galerie"
			multiple: true
		file:
			type: file
			label: "Soubor"
		files:
			type: file
			label: "Soubory"
			multiple: true
		colorTheme:
			type: select
			label: "Výběr barevné varianty stránky"
			defaultValue: false
			options: [ {
				label: "Šedá (defaultní)"
				value: "gray"
			}, {
				label: "Hnědá"
				value: "brown"
			}]

		numbers:
			type: group
			label: "Čísla"
			items:
				subtitle:
					type: text
					label: "Podnadpis"
					translatable: true
				title:
					type: text
					label: "Nadpis"
					translatable: true
				content:
					type: textarea
					label: "Obsah"
					translatable: true
				highlightFirst:
					type: checkbox
					label: "Zvýraznit první polož<PERSON>"
				variantSmall:
					type: checkbox
					label: "<PERSON><PERSON> pís<PERSON>"
				items:
					type: list
					label: "Položky"
					items:
						value:
							type: text
							label: "Hodnota"
						image:
							type: image
							label: "Obrázek / ikona (180x180)"
						title:
							type: text
							label: "Nadpis"
							translatable: true
						title2:
							type: text
							label: "Nadpis (druhý řádek)"
							translatable: true
						desc:
							type: textarea
							label: "Popis"
							translatable: true
				spacing:
					@cf.definitions.spacing
				fixedMenuName:
					@cf.definitions.fixedMenuName

		image1:
			type: group
			label: "Obrázek 1"
			items:
				image:
					type: image
					label: "Obrázek (1416x850)"
				alt:
					type: text
					label: "Alternativní text"
					translatable: true
				desc:
					type: textarea
					label: "Popis"
					translatable: true

		contentPhotoImage:
			type: group
			label: "Obrázek"
			items:
				src:
					type: image
					label: "Vložení obrázku"
				text:
					type: text
					label: "Popis obrázku"
					translatable: true

		introMedia:
			type: group
			label: "Medium pro úvodní sekci"
			items:
				imgs:
					@cf.definitions.imgs
				file:
					type: file
					label: "Videosoubor"
				youtube:
					type: text
					label: "Odkaz na Youtube / Vimeo"
				autoplay:
					type: checkbox
					label: "Automatické přehrávání + loop"
				poster:
					type: image
					label: "Zástupný obrázek videa (1920x1280)"

		introMediaArticle:
			type: group
			label: "Medium pro úvodní sekci"
			items:
				imgs:
					@cf.definitions.imgs_article
				file:
					type: file
					label: "Videosoubor"
				youtube:
					type: text
					label: "Odkaz na Youtube / Vimeo"
				autoplay:
					type: checkbox
					label: "Automatické přehrávání + loop"
				poster:
					type: image
					label: "Zástupný obrázek videa (1920x1080)"

		socialImages:
			type: group
			label: "Obrázky pro sociální sdílení"
			items:
				fb:
					type: image
					label: "Facebook (1200x630)"
				tw:
					type: image
					label: "Twitter (500x500)"
		redirectURL:
			type: group
			label: "URL kam stránku přesměrovat"
			items:
				url:
					type: text
					label: "URL"

		introFiles:
			type: group
			label: "Soubory pro úvodní sekci"
			items:
				file:
					type: file
					label: "Vyberte soubory"
					multiple: true

		subMenu:
			type: group
			label: "Banner v hlavním menu"
			items:
				img:
					type: image
					label: "Obrázek (360x532)"
				btn:
					type: text
					label: "Text tlačítka"
					translatable: true
				src:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu"
					url: @cf.suggestUrls.searchMutationPage
				external:
					type: text
					label: "Odkaz (nezapomeňte na https://)"
					placeholder: "https://..."

		footerLinks.title:
			type: text
			label: "Nadpis"
			translatable: true
		footerLinks.list:
			extends: @cf.definitions.linkChoose
			type: list
			label: "Položky menu"

		spacing:
			type: group
			label: "Odsazení komponenty"
			items:
				desktop:
					type: text
					label: "Desktop (Zadejte číslo 0 – 29)"
					defaultValue: "15"
				tablet:
					type: text
					label: "Tablet (Zadejte číslo 0 – 29)"
					defaultValue: "7"
				mobile:
					type: text
					label: "Mobil (Zadejte číslo 0 – 29)"
					defaultValue: "5"

		linkChoose:
			type: group
			label: "Odkazy"
			items:
				page:
					type: suggest
					label: "Odkaz v rámci webu "
					subType: "tree"
					url: @cf.suggestUrls.searchMutationPage
				text:
					type: text
					label: "Text odkazu"
					placeholder: "Zadejte poukud se liší od názvu stránky nebo se jedná o externí odkaz"
					translatable: true
				external:
					type: text
					label: "Odkaz (nezapomeňte na https://)"
					placeholder: "https://..."

		linkChooseExtended:
			type: group
			label: "Odkazy"
			items:
				page:
					type: suggest
					label: "Odkaz v rámci webu"
					subType: "tree"
					url: @cf.suggestUrls.searchMutationPage
				reference:
					type: suggest
					label: "Odkaz na referenci"
					subType: "reference"
					url: @cf.suggestUrls.searchMutationReference
				event:
					type: suggest
					label: "Odkaz na událost"
					subType: "event"
					url: @cf.suggestUrls.searchMutationEvents
				article:
					type: suggest
					label: "Odkaz na článek"
					subType: "blog"
					url: @cf.suggestUrls.searchMutationBlogs
				text:
					type: text
					label: "Text odkazu"
					placeholder: "Zadejte poukud se liší od názvu stránky nebo se jedná o externí odkaz"
					translatable: true
				external:
					type: text
					label: "Odkaz (nezapomeňte na https://)"
					placeholder: "https://..."

		imgs:
			type: group
			label: "Obrázky do úvodní sekce"
			items:
				img_desktop:
					type: image
					label: "Desktop (3840x2560)"
				img_tablet:
					type: image
					label: "Tablet (1000x670)"
				img_mobile:
					type: image
					label: "Mobil (750x570)"

		imgs_article:
			type: group
			label: "Obrázky do úvodní sekce"
			items:
				img_desktop:
					type: image
					label: "Desktop (3840x2560)"
				img_tablet:
					type: image
					label: "Tablet (1000x670)"
				img_mobile:
					type: image
					label: "Mobil (750x570)"

		fixedMenuName:
			type: text
			label: "Název ve fixním menu"
			translatable: true

		store:
			type: suggest
			label: "Výběr pobočky"
			subType: contact
			url: @cf.suggestUrls.searchMutationContact

	fields:
		annotation:
			@cf.definitions.annotation
		imgs:
			@cf.definitions.imgs

		imgs_article:
			@cf.definitions.imgs_article

		social_images:
			@cf.definitions.socialImages

		redirectURL:
			@cf.definitions.redirectURL

		img_crossroad:
			type: image
			label: "Obrázek pro výpis a detail (3840x3840)"
		intro_media:
			@cf.definitions.introMedia
		intro_media_article:
			@cf.definitions.introMediaArticle
		intro_files:
			@cf.definitions.introFiles
		intro_content:
			type: tinymce
			label: "Obsah do úvodní sekce"
		position:
			type: text
			label: "Pozice, role, funkce"

		colorTheme:
			type: group
			label: "Výběr barevné varianty stránky"
			items:
				colorTheme:
					@cf.definitions.colorTheme

		contacts:
			type: list
			label: "Kontakty"
			items:
				type:
					label: "Typ kontaktu (ikona)"
					type: select
					options: [ {
						label: "Telefon"
						value: "phone"
					}, {
						label: "E-mail"
						value: "mail"
					}, {
						label: "Webová stránka"
						value: "website"
					}]
				value:
					type: text
					label: "Hodnota"
				hours:
					type: text
					label: "Kontaktní doba (volitelné)"
				info:
					type: text
					label: "Dodatečná informace (volitelné)"
		company:
			type: text
			label: "Společnost"

		lat:
			type: text
			label: "Zeměpisná šířka"
		lng:
			type: text
			label: "Zeměpisná délka"

		map_link:
			type: group
			label: "Odkaz na Google Mapu"
			items:
				link:
					type: text
					label: "Odkaz"

		intro_btns:
			type: list
			label: "Tlačítka v záhlaví"
			items:
				link:
					extends: @cf.definitions.linkChoose

		addressGroup:
			type: group
			label: "Adresa"
			items:
				street:
					type: text
					label: "Ulice"
				city:
					type: text
					label: "Město"
				zip:
					type: text
					label: "PSČ"
				state:
					type: text
					label: "Stát (volitelné)"
				country:
					type: text
					label: "Země"


		sub_menu_bnr:
			@cf.definitions.subMenu
		sub_menu_bnr_2:
			@cf.definitions.subMenu
		sub_menu_img:
			type: image
			label: "Obrázek pro submenu (208x176)"

#		submenuSeoLinkReference:
#			type: list
#			label: "SeoLinky v submenu"
#			items:
#				image:
#					type: image
#					label: "Obrázek (208x176)"
#				page:
#					type: suggest
#					subType: seolink
#					label: "Odkaz v rámci webu"
#					url: @cf.suggestUrls.searchMutationSeoLinkReference
#				external:
#					type: text
#					label: "Odkaz (nezapomeňte na https://)"
#					placeholder: "https://..."

		social:
			type: group
			label: "Sociální sítě"
			items:
				facebook:
					type: text
					label: "Facebook"
				email:
					type: text
					label: "E-mail"
				instagram:
					type: text
					label: "Instagram"
				pinterest:
					type: text
					label: "Pinterest"
				youtube:
					type: text
					label: "Youtube"
				linkedin:
					type: text
					label: "Linkedin"
		submenuPages:
			type: list
			label: "Stránky v submenu"
			items:
				page:
					type: suggest
					subType: tree
					label: "Odkaz v rámci webu "
					url: @cf.suggestUrls.searchMutationPage
				nameAnchor:
					type: text
					label: "text odkazu"
					translatable: true
				external:
					type: text
					label: "Odkaz (nezapomeňte na https://)"
					placeholder: "https://..."
				sub_menu_img:
					type: image
					label: "Obrázek pro submenu (208x176)"

		cookies:
			label: "Cookies"
			type: group
			items:
				title:
					label: "Nadpis"
					type: text
					value: ""
					translatable: true
				text:
					label: "Text"
					type: tinymce
					value: ""
				btnSetPreferences:
					label: "Tlačítko - nastavit preference"
					type: text
					value: ""
					translatable: true
				btnReject:
					label: "Tlačítko - Odmítnout"
					type: text
					value: ""
					translatable: true
				btnConsentAndContinuation:
					label: "Tlačítko - souhlas a pokračování"
					type: text
					value: ""
					translatable: true
				consentsTitle:
					label: "Nadpis - nastavení preferencí"
					type: text
					value: ""
					translatable: true
				necessarilyLink:
					label: "Nezbytné - link"
					type: text
					value: ""
					translatable: true
				necessarilyText:
					label: "Nezbytné - text"
					type: tinymce
					value: ""
					translatable: true
				preferenceslLink:
					label: "Předvolby - link"
					type: text
					value: ""
					translatable: true
				preferencesText:
					label: "Předvolby - text"
					type: tinymce
					value: ""
					translatable: true
				analyticsLink:
					label: "Analytika - link"
					type: text
					value: ""
					translatable: true
				analyticsText:
					label: "Analytika - text"
					type: tinymce
					value: ""
					translatable: true
				marketingLink:
					label: "Marketingové - link"
					type: text
					value: ""
					translatable: true
				marketingText:
					label: "Marketingové - text"
					type: tinymce
					value: ""
					translatable: true
				btnConfirmSelected:
					label: "Tlačítko - potvrdit vybrané"
					type: text
					value: ""
					translatable: true
				btnAcceptEverything:
					label: "Tlačítko - přijmout vše"
					type: text
					value: ""
					translatable: true

#		submenuSeoLinkProduct:
#			type: list
#			label: "SeoLinky v submenu"
#			items:
#				image:
#					type: image
#					label: "Obrázek"
#				page:
#					type: suggest
#					subType: seolink
#					label: "Odkaz v rámci webu "
#					url: @cf.suggestUrls.searchMutationSeoLinkProduct
#				external:
#					type: text
#					label: "Odkaz (nezapomeňte na https://)"
#					placeholder: "https://..."




		file:
			type: group
			label: "Soubor"
			items:
				file:
					type: file
					label: "Soubor"

		file_multiple:
			type: group
			label: "Soubory"
			items:
				file_multiple:
					type: file
					label: "Soubory"
					multiple: true

		name:
			type: group
			label: "Name"
			items:
				name:
					type: text
					label: "Name"
					translatable: true
					# defaultValue: "John Doe"
		mutationData:
			type: group
			label: "Nastavení mutace"
			items:
				icon:
					type: text
					label: "jméno svg ikony v přepínači jazyků"
					# defaultValue: "John Doe"
				ogImage:
					type: image
					label: "og:image - obrázkový náhled webu (1200x630)"

		image:
			type: group
			label: "Image"
			items:
				image:
					type: image
					label: "Image"

		image_multiple:
			type: group
			label: "Image multiple"
			items:
				image_multiple:
					type: image
					label: "Image multiple"
					multiple: true

		base:
			type: group
			items:
				mainImage:
					type: image
					label: "Obrázek pro rozcestník"

		suggest:
			type: group
			label: "Suggest tree"
			items:
				suggest:
					type: suggest
					subType: "tree"
					label: "Suggest tree"
					url: @cf.suggestUrls.searchMutationPage

		alias:
			type: group
			label: "Suggest alias"
			items:
				alias:
					type: suggest
					subType: "alias"
					label: "Suggest alias"
					url: @cf.suggestUrls.searchMutationAlias

		description:
			type: group
			label: "Description"
			items:
				description:
					type: textarea
					label: "Description"
					translatable: true
					# placeholder: ""

		tinymce:
			type: group
			label: "Tinymce"
			items:
				tinymce:
					type: tinymce
					label: "Tinymce"
					translatable: true

		checkbox:
			type: group
			label: "Checkbox"
			items:
				checkbox:
					type: checkbox
					label: "Checkbox"
					defaultValue: true
				checkbox2:
					type: checkbox
					label: "Checkbox2"

		radio:
			type: group
			label: "Radio"
			items:
				radio:
					type: radio
					label: "Radio"
					defaultValue: "first"
					options: [
						{ label: "Prvni", value: "first" },
						{ label: "Druha", value: "second" }
					]

		select:
			type: group
			label: "Select"
			items:
				select:
					type: select
					label: "Select"
					hideNoValueOption: true
					options: [
						{ label: "Prvni", value: "first" },
						{ label: "Druha", value: "second" }
					]

		select_multiple:
			type: group
			label: "Select multiple"
			items:
				select_multiple:
					type: select
					label: "Select multiple"
					multiple: true
					options: [
						{ label: "Prvni", value: "first" },
						{ label: "Druha", value: "second" }
					]

		message:
			type: group
			label: "Tohle je needitovatelna zprava"
			items:
				message:
					type: message
					variant: ok
					# variant: "ok" / "error" / "warning"
					label: "Tohle je needitovatelna zprava"

		list:
			type: list
			label: "List 1"
			items:
				demopage:
					type: suggest
					subType: tree
					label: "Zadej URL"
					url: @cf.suggestUrls.searchMutationPage
					order: 2
				firstName:
					type: text
					label: "Zadej jméno"
					order: 1
				lastName:
					type: text
					label: "Zadej jméno"
					order: 3

		parameterForFilter:
			type: group
			label: "Nastavení filtru"
			items:
				visibleParameters:
					type: list
					label: Parametry
					items:
						indexable:
							type: checkbox
							label: "Indexovatelné"
						visibleCount:
							type: text
							label: "Počet viditelných hodnot"
						parameter:
							type: suggest
							subType: parameter
							placeholder: Jméno parametru
							url: @cf.suggestUrls.searchParameterForFilter


						numberAsRange:
							type: checkbox
							label: "Rozsah pro číselné hodnoty"

		toggleRadio:
			type: group
			label: "Nastavení kategorií feedů"
			hasContentToggle: true
			items:
				toggle:
					type: radio
					inline: true
					isContentToggle: true
					options: [
						{ label: "Zboží", value: "zbozi" },
						{ label: "Heureka", value: "heureka" },
						{ label: "Skupina", value: "group" },
					]
				zbozi:
					type: text
				heureka:
					type: textarea
				group:
					type: group
					label: "Demo skupina"
					items:
						groupItem2:
							type: text
							label: "groupItem2"
						groupItem1:
							type: text
							label: "groupItem1"


		feeds:
			type: group
			label: "Nastavení kategorií feedů"
			items:
				zbozi:
					type: text
					label: "Zboží"
				heureka:
					type: text
					label: "Heureka"
				google:
					type: text
					label: "Google"

		userMenuLoggedUser:
			label: "User menu pro přihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		userMenuUnloggedUser:
			label: "User menu pro nepřihlášeného (hlavička)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		userSideMenu:
			label: "User side menu (uživatelská sekce)"
			type: list
			items:
				tree:
					type: suggest
					label: "Stránka"
					subType: tree
					url: @cf.suggestUrls.searchMutationPage

		footer_menu_1:
			type: group
			label: "Footer menu – 1. sloupec"
			items:
				title: @cf.definitions.footerLinks.title
				list: @cf.definitions.footerLinks.list
		footer_menu_2:
			type: group
			label: "Footer menu – 2. sloupec"
			items:
				title: @cf.definitions.footerLinks.title
				list: @cf.definitions.footerLinks.list
		footer_menu_3:
			type: group
			label: "Footer menu – 3. sloupec"
			items:
				title: @cf.definitions.footerLinks.title
				list: @cf.definitions.footerLinks.list
		intro_hp:
			type: group
			label: "Intro na hlavní stránce"
			hasContentToggle: true
			items:
				viewType:
					type: radio
					inline: true
					isContentToggle: true
					label: "Typ zobrazení"
					options: [
						{ label: "Obrázek", value: "imageGroup" },
						{ label: "Carousel s videi", value: "carouselGroup" },
						{ label: "Carousel s foto/video a obsahem", value: "carouselMixGroup" },
						{ label: "Loop video", value: "loopGroup" }
					]
				loop:
					type: checkbox
					label: "Automatické loopování"
					defaultValue: true
				imageGroup:
					type: group
					label: "Obrázek"
					items:
						imgs: # todo
							type: group
							label: "Obrázky do úvodní sekce"
							items:
								img_desktop:
									type: image
									label: "Desktop (3840x2560)"
								img_tablet:
									type: image
									label: "Tablet (1500x2000)"
								img_mobile:
									type: image
									label: "Mobil (750x1500)"
						btn:
							extends: @cf.definitions.linkChooseExtended
							label: "Tlačítko"
				carouselMixGroup:
					type: group
					label: "Carousel s foto/video a obsahem"
					items:
						items:
							type: list
							label: "Slidy"
							items:
								image:
									type: image
									label: "Obrázek nebo zástupný obrázek videa (1920x1080)"
								video_file:
									type: file
									label: "Video soubor"
								video_url:
									type: text
									label: "Odkaz na Youtube / Vimeo"
								title:
									type: text
									label: "Nadpis"
								annot:
									type: textarea
									label: "Obsah"
								btn:
									extends: @cf.definitions.linkChooseExtended
									label: "Tlačítko"

				carouselGroup:
					type: group
					label: "Carousel s videi"
					items:
						autoplay:
							type: checkbox
							label: "Automatické přehrávání"
							default: true
						poster:
							type: image
							label: "Zástupný obrázek videí (1920x1080)"
						videos:
							type: list
							label: "Videa do úvodní sekce"
							items:
								link:
									type: text
									label: "Odkaz na Youtube / Vimeo"
								text:
									type: text
									label: "Text záložky (první řádek)"
								textItalic:
									type: text
									label: "Text záložky kurzívou (druhý řádek)"
				loopGroup:
					type: group
					label: "Loop video"
					items:
						poster:
							type: image
							label: "Zástupný obrázek videí (1920x1080)"
						video:
							type: file
							label: "Video soubor"
						btn:
							extends: @cf.definitions.linkChooseExtended
							label: "Tlačítko"

		inverted_header:
			type: group
			items:
				inverted:
					label: "Invertované záhlaví"
					type: select
					defaultValue: "automatic"
					options: [
						{ label: "Neinvertované záhlaví", value: "false" }
						{ label: "Invertované záhlaví", value: "true" }
					]

		hide_header:
			type: group
			label: "Skrýt hlavičku na této stránce"
			items:
				hidden:
					label: "Skrýt hlavičku"
					type: checkbox
					defaultValue: false

	suggestUrls:
		searchMutationContact:
			searchParameterName: search
			link: "/superadmin/search2/contact-in-mutation"
			params: []
		searchState:
			searchParameterName: search
			link: "/superadmin/search2/state"
			params: []
		searchDesigners:
			searchParameterName: search
			link: "/superadmin/search2/designers"
			params: []
		searchPerson:
			searchParameterName: search
			link: "/superadmin/search2/person"
			params: []
		searchMutationSeoLinkReference:
			searchParameterName: search
			link: "/superadmin/search2/seolink-in-mutation"
			params: [type: product]
		searchMutationSeoLinkProduct:
			searchParameterName: search
			link: "/superadmin/search2/seolink-in-mutation"
			params: [type: product]
		searchDesignersInMutation:
			searchParameterName: search
			link: "/superadmin/search2/designer-in-mutation"
		searchEventInMutation:
			searchParameterName: search
			link: "/superadmin/search2/event-in-mutation"
		searchMutationPerson:
			searchParameterName: search
			link: "/superadmin/search2/person-in-mutation"
			params: []
		searchMutationPress:
			searchParameterName: search
			link: "/superadmin/search2/press-in-mutation"
			params: []
		searchMutationReference:
			searchParameterName: search
			link: "/superadmin/search2/reference-in-mutation"
			params: []
		searchFolder:
			searchParameterName: search
			link: "/superadmin/search2/folder"
			params: []
		searchBlogsInMutation:
			searchParameterName: search
			link: "/superadmin/search2/blog-in-mutation"
			params: []
		searchBlogs:
			searchParameterName: search
			link: "/superadmin/search2/blog"
			params: []
		searchReferencesInMutation:
			searchParameterName: search
			link: "/superadmin/search2/reference-in-mutation"
			params: []
		searchReferences:
			searchParameterName: search
			link: "/superadmin/search2/reference"
			params: []
		searchParameterValueByUid.typeReferences:
			searchParameterName: search
			link: "/superadmin/search2/parameter-value?uids=type-references"

		searchParameterValueByUid.interior:
			searchParameterName: search
			link: "/superadmin/search2/parameter-value?uids=interior"

		searchParameterValueByUid.location:
			searchParameterName: search
			link: "/superadmin/search2/parameter-value?uids=location"
		searchMutationPage:
			searchParameterName: search
			link: "/superadmin/search2/page-in-mutation"
			params: []
		searchBlogTag:
			searchParameterName: search
			link: "/superadmin/search2/blog-tag"
			params: []
		searchPage:
			searchParameterName: search
			link: "/superadmin/search2/page"
			params: []
		searchProduct:
			searchParameterName: search
			link: "/superadmin/search2/product"
			params: []
		searchParameter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: []
		searchParameterForFilter:
			searchParameterName: search
			link: "/superadmin/search2/parameter"
			params: [ 'types': ['select', 'multiselect', 'number'], 'onlyForFilter': 1]
		searchSeoLinkParameterValues:
			searchParameterName: search
			link: "/superadmin/search2/seolink-parameter-values"
			params: []
		searchAuthors: <AUTHORS>
			link: "/superadmin/search2/author"
			params: []
		searchMutationBlogs:
			searchParameterName: search
			link: "/superadmin/search2/blog-in-mutation"
			params: []
		searchMutationEvents:
			searchParameterName: search
			link: "/superadmin/search2/event-in-mutation"
			params: []
		searchMutationAlias:
			searchParameterName: search
			link: "/superadmin/search2/alias"
			params: []
		searchCountry:
			searchParameterName: search
			link: "/superadmin/search2/country"
			params: []
	templates:
		#template: [customfields]

		# examples - todo to WIKI:
		# WIKI - https://www.notion.so/superkoders/Vlastn-pole-Custom-Fields-verze-1-0-2c3322c358224c769c0bdb1a9593b6d2
		#1) :Front:Page:default: [customfields] # CF pro stránku s šablonou Page:default
		#2) Product:detail: [customfields] # CF pro produkt
		#3) product-AAA: [customfields] # CF pro produkt jehož nejvyšším rodičem (hlavní katgorie) je UID produktové kategorie AAA
		#4) uid-XXX: [customfields] # CF pro stránku s uid = XXX
		#5) parameter-YYY: [customfields] #CF pro parametr s uid = YYY
		#6) parameters: [customfields] #CF pro všechny parametry obecně, parameter-YYY přebíjí
		#7) banner-ZZZ: [customfields] #CF pro banner s pozici = ZZZ
		#8) CF pro všechny stránky ve stromě, nasatvuje se do speciální sekce: customFieldsTemplates
		#9) user-ROLE || users


		:Front:Homepage:default: [@cf.inverted_header, @cf.hide_header, @cf.social_images, @cf.footer_menu_1, @cf.footer_menu_2, @cf.footer_menu_3, @cf.intro_hp]

		:Front:Page:bespoke: [@cf.inverted_header, @cf.hide_header, @cf.intro_media, @cf.social_images]
		:Front:Page:signature: [@cf.colorTheme, @cf.inverted_header, @cf.hide_header, @cf.intro_media_article, @cf.social_images, @cf.intro_btns]
		:Front:Page:contact: [@cf.inverted_header, @cf.hide_header, @cf.social, @cf.social_images]
		:Front:Page:references: [@cf.inverted_header, @cf.hide_header, @cf.sub_menu_img, @cf.social_images]
		:Front:Page:about: [@cf.inverted_header, @cf.hide_header, @cf.intro_media_article, @cf.social_images]

		:Press:Front:Press:default: [@cf.inverted_header, @cf.hide_header, @cf.intro_media]

		Catalog:default: [@cf.inverted_header, @cf.hide_header, @cf.sub_menu_img, @cf.parameterForFilter, @cf.submenuPages, @cf.social_images]
		Product:detail: [@cf.inverted_header, @cf.hide_header, @cf.intro_media, @cf.intro_files, @cf.social_images]
		Fake:product: [@cf.inverted_header, @cf.hide_header, @cf.parameterForFilter, @cf.social_images]

		"uid-cookie": [@cf.inverted_header, @cf.hide_header, @cf.cookies, @cf.social_images]
		"uid-references": [@cf.inverted_header, @cf.hide_header, @cf.sub_menu_bnr, @cf.sub_menu_bnr_2, @cf.social_images]
		"uid-404": [@cf.inverted_header, @cf.hide_header, @cf.intro_content, @cf.intro_media, @cf.social_images]
		"uid-eshop": [@cf.inverted_header, @cf.hide_header, @cf.sub_menu_bnr, @cf.social_images, @cf.redirectURL]

		:Reference:Front:Reference:default: [@cf.inverted_header, @cf.hide_header, @cf.sub_menu_img, @cf.parameterForFilter, @cf.submenuPages, @cf.social_images]

		# :Front:Page:cheatsheet3: [@cf.colorTheme]





#		mutation: [@cf.mutationData]
#		uid-userSection: [@cf.userMenuUnloggedUser, @cf.userMenuLoggedUser, @cf.userSideMenu]
#		:Front:Product:detail: [@cf.base]
#		:Front:Page:default: [@cf.base]
#		:Front:Catalog:default: [@cf.toggleRadio, @cf.parameterForFilter, @cf.base]
#		parameterValue: [@cf.image]
