	services:
		#FORM/COMPONENT FACTORIES
		- App\Components\VisualPaginator\VisualPaginatorFactory

		-
		    implement: App\AdminModule\Components\SignInForm\SignInFormFactory
		    inject: true

		-
		    implement: App\AdminModule\Components\LostPasswordForm\LostPasswordFormFactory
		    inject: true

		-
		    implement: App\AdminModule\Components\ResetPasswordForm\ResetPasswordFormFactory
		    inject: true

		-
		    implement: App\FrontModule\Components\ContactForm\ContactFormFactory
		    inject: true
		-
		    implement: App\AdminModule\Components\SynonymsForm\SynonymsFormFactory
		    inject: true
		-
			implement: App\FrontModule\Components\NewsletterForm\NewsletterFormFactory
			inject: true

		-
			implement: App\FrontModule\Components\SignInForm\SignInFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\ProfileForm\ProfileFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\ChangePasswordForm\ChangePasswordFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\RegistrationForm\RegistrationFormFactory
			inject: true
		-
			implement: App\FrontModule\Components\LostPasswordForm\LostPasswordFormFactory
			inject: true
		- App\Components\MessageForForm\MessageForFormFactory
		-
			implement: App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory
			inject: true
		-
			implement: App\FrontModule\Components\Menu\MenuFactory
			inject: true

		-
			implement: App\FrontModule\Components\Robots\RobotsFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserMenu\UserMenuFactory
			inject: true

		-
			implement: App\FrontModule\Components\Modal\ModalFactory
			inject: true
		-
			implement: App\FrontModule\Components\UserSideMenu\UserSideMenuFactory
			inject: true

		-
			implement: App\FrontModule\Components\Breadcrumb\BreadcrumbFactory
			inject: true


		- App\FrontModule\Components\ProductParameters\ProductParametersFactory


		- App\AdminModule\Components\ApiTokenDataGrid\ApiTokenDataGridFactory
		- App\AdminModule\Components\ApiTokenShellForm\ApiTokenShellFormFactory


		- implement: App\FrontModule\Components\GoogleConnect\GoogleConnectFactory
		  arguments: { isEnabled: %google.oauth.isPublic% }

		- implement: App\FrontModule\Components\GoogleLogin\GoogleLoginFactory
		  arguments: { isEnabled: %google.oauth.isPublic% }

		- implement: App\AdminModule\Components\GoogleLogin\GoogleLoginFactory
		  arguments: { isEnabled: ::boolval(%google.oauth.clientId%) }

