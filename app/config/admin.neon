services:
	- App\Model\AdminEmails(
		adminEmailDomains: %emailDomains.admin%
		developerEmailDomains: %emailDomains.developer%
	)

parameters:
	emailDomains:
		admin: []
		developer: []

	config:
		adminMenu:
			Modules:
				- {title: Library, resource: Admin:Library, action: :Admin:Library:default, icon: photo-video}
				- {title: Pages, resource: Page:Admin:Page, action: :Page:Admin:Page:default, icon: stream}
				- {title: Products, resource: Admin:Catalog, action: :Admin:Catalog:default, icon: lightbulb}
				- {title: Seolink, resource: SeoLink:Admin:SeoLink, action: :SeoLink:Admin:SeoLink:default, icon: file}
				- {title: Modal, resource: Modal:Admin:Modal, action: :Modal:Admin:Modal:default, icon: file}
				- {title: Novinky, resource: Blog:Admin:Blog, action: :Blog:Admin:Blog:default, icon: newspaper}
				#- {title: BlogTag, resource: BlogTag:Admin:BlogTag, action: :BlogTag:Admin:BlogTag:default, icon: tags, sub: true}
				- {title: Author, resource: Author:Admin:Author, action: :Author:Admin:Author:default, icon: user-edit, sub: true}
				- {title: Designers, resource: Designer:Admin:Designer, action: :Designer:Admin:Designer:default, icon: drafting-compass}
				- {title: References, resource: Reference:Admin:Reference, action: :Reference:Admin:Reference:default, icon: book-reader}
				- {title: Press, resource: Press:Admin:Press, action: :Press:Admin:Press:default, icon: newspaper}
				- {title: Persons, resource: Person:Admin:Person, action: :Person:Admin:Person:default, icon: user-friends}
				- {title: Events, resource: Event:Admin:Event, action: :Event:Admin:Event:default, icon: calendar-alt}
				- {title: Offices, resource: Contact:Admin:Contact, action: :Contact:Admin:Contact:default, icon: building}


			Lists: # veci které se plní frontenedem
				- {title: E-mails, resource: Admin:Newsletter, action: :Admin:Newsletter:default, icon: at}

			Settings:
				- {title: System strings, resource: Admin:String, action: :Admin:String:default, icon: language}
				- {title: Mutations, resource: Admin:Mutation, action: :Admin:Mutation:default, icon: globe}
				- {title: States, resource: Admin:State, action: :Admin:State:default, icon: flag}
				- {title: Trash, resource: Admin:Trash, action: :Admin:Trash:default, icon: trash}
				- {title: Parameters, resource: Admin:Parameter, action: :Admin:Parameter:default, icon: file}
				- {title: Users, resource: Admin:User, action: :Admin:User:default, icon: user}
				- {title: E-mails template, resource: Admin:Email, action: :Admin:Email:default, icon: envelope}
				- {title: Redirects, resource: Admin:Redirect, action: :Admin:Redirect:default, icon: map-signs}
			-
#				- {title: Šablony, resource: Admin:Template, action: :Admin:Template:default, icon: file}
				- {title: Elastic search, resource: Admin:Elastic, action: :Admin:Elastic:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: Cache, resource: Admin:Cache, action: :Admin:Cache:default, icon: file, devOnly: true} #vidi jen developeri
				- {title: API tokens, resource: Admin:ApiToken, action: :Admin:ApiToken:default, icon: file, devOnly: true}
				- {title: Help, resource: superadmin, action: :Admin:Help:default, icon: info-circle}
				- {title: About, resource: superadmin, action: :Admin:Help:about, icon: info-circle}

			# -
				# - {title: Styleguide, resource: Admin:Styleguide, action: :Admin:Styleguide:default, icon: file, devOnly: true}


		modules:
			Admin:Homepage: Homepage
			Page:Admin:Page: Page
			Blog:Admin:Blog: Blog
			#BlogTag:Admin:BlogTag: BlogTag
			Author:Admin:Author: Author
			Admin:Library: Library
			Admin:File: File
			Admin:Catalog: Catalog
			Admin:Product: Products
			Admin:User: Users
			Admin:Parameter: Parameters
			Admin:Email: E-mails
			Admin:String: System strings
			Admin:Developer: Developerské nastavení
			Admin:Help: Help
			Admin:Newsletter: Newsletter
			Admin:Place: Place
			Admin:Search: Search
			Admin:Search2: Search
			Admin:Template: Templates
			Admin:Redirect: Redirect
			Admin:Mutation: Mutation
			Admin:Elastic: Elastic
			Admin:State: State
			Admin:Trash: Trash
			Admin:Styleguide: Styleguide
			SeoLink:Admin:SeoLink: Seolink
			Modal:Admin:Modal: Modal
			Admin:Cache: Cache
			Admin:ApiToken: API tokeny
			Designer:Admin:Designer: Designer
			Reference:Admin:Reference: Reference
			Press:Admin:Press: Press
			Person:Admin:Person: Person
			Event:Admin:Event: Event
			Contact:Admin:Contact: Contact

		adminPaging: 20
		adminImagePaging: 30
		productPaging: 20

		tabs:
			pages: [
				images,
				videos,
				files,
				linkedCategories,
				seo,
				settings,
			]
			products: [
				variants,
				content,
				images,
				files,
				params,
				links,
				videos,
				reviews,
				articlePages,
				accessories, # doporucujeme
				presents,
				similar, #podobne produkty
				product, #podobne produkty pro vyprodany produkt
				contents,
				seo,
				settings,
			]
			emailTemplates: [
				files,
			]

		tabsHideOnlyOnTemplates:
			pages:
#				files:
#				links:
#				videos:
				attproducts:
#					- Article:detail
#					- Article:default
				pages:
#					- Article:detail
#					- Article:default
				faqs:
#					- Article:detail
#					- Article:default
				reviews:
#					- Article:detail
#					- Article:default
				attproductsReview:
#					- Article:detail
#					- Article:default



		tabsShowOnlyOnTemplates: # note: tabs are defined in admin.neon
			pages:
				params:
#					- Article:detail
				linkedCategories:
					- Catalog:default
#				faqs:
#				reviews:

includes:
	- lang/lang.neon
