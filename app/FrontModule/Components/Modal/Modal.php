<?php

declare(strict_types=1);

namespace App\FrontModule\Components\Modal;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\State\State;
use App\PostType\Modal\Model\Orm\ModalLocalization;
use App\PostType\Modal\Model\Orm\ModalLocalizationRepository;
use Nette\Application\AbortException;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\Json;
use Nette\Utils\JsonException;

/**
 * @property-read DefaultTemplate $template
 */
final class Modal extends Control
{
	public const COOKIE_CLOSED_MODAL_IDS = 'closedModalIds';

	public function __construct(
		private readonly Mutation $mutation,
		private readonly State $currentState,
		public readonly ModalLocalizationRepository $modalLocalizationRepository,
	)
	{
	}

	public function render(): void
	{
		$this->template->modal = $this->getModal();
		$this->template->render(__DIR__ . '/modal.latte');
	}

	/**
	 * @return array<mixed>
	 */
	private function getModal(): array
	{
		$closedModalIds = $this->presenter->getHttpRequest()->getCookie(self::COOKIE_CLOSED_MODAL_IDS) ?? [];

		$closedModalIdsArray = [];

		if (is_string($closedModalIds)) {
			$closedModalIdsArray = Json::decode($closedModalIds);
		}

		$localizations = $this->modalLocalizationRepository->findPublishedInMutationAndState($this->mutation, $this->currentState, $closedModalIdsArray, 1);
		$modalLocalization = $localizations->fetch();

		if (!$modalLocalization instanceof ModalLocalization) {
			return [];
		}

		return [
			'id' => $modalLocalization->modal->id,
			'cf' => $modalLocalization->modal->cf,
		];
	}


	/**
	 * @throws JsonException
	 * @throws AbortException
	 */
	public function handleCloseModal(int $id): void
	{
		$closedModalIds = $this->presenter->getHttpRequest()->getCookie(self::COOKIE_CLOSED_MODAL_IDS) ?? [];

		$closedModalIdsArray = [];

		if (is_string($closedModalIds)) {
			$closedModalIdsArray = Json::decode($closedModalIds);
		}

		if (!in_array($id, $closedModalIdsArray)) {
			$closedModalIdsArray[] = $id;
			$this->presenter->getHttpResponse()->setCookie(self::COOKIE_CLOSED_MODAL_IDS, Json::encode($closedModalIdsArray), '1 year');
		}

		if ($this->presenter->isAjax()) {
			$this->presenter->payload->success = true;
			$this->presenter->sendPayload();
		} else {
			$this->presenter->redirect('this');
		}
	}

}
