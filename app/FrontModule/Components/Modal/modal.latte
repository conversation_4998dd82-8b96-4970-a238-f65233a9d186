{block content}

	{if $modal !== []}
		<div id="modal-{$modal['id']}" data-dynamic-modal {if isset($modal['cf']->colorTheme->colorTheme) && $modal['cf']->colorTheme->colorTheme}data-dynamic-modal-color="{$modal['cf']->colorTheme->colorTheme}"{/if} style="display: none;">
			<div class="b-modal__custom b-custom">
				<div class="b-custom__buttons">
					<button type="button" class="b-custom__fullscreen js-modal-fullscreen">
						<span class="u-vhide">Fullscreen</span>
						<span class="icon-svg icon-svg--fullscreen">
							{('fullscreen-thick')|icon}
						</span>
					</button>
					<button type="button" class="b-custom__close js-modal-close">
						<span class="u-vhide">Close</span>
						<span class="icon-svg icon-svg--close-thick">
							{('close-thick')|icon}
						</span>
					</button>
				</div>

				{if isset($modal['cf']->intro_media->imgs) && $modal['cf']->intro_media->imgs->img_desktop && $modal['cf']->intro_media->imgs->img_tablet && $modal['cf']->intro_media->imgs->img_mobile}
					<div class="b-custom__image">
						<picture>
							{var $imageDesktop = $modal['cf']->intro_media->imgs->img_desktop->getSize('intro_article_desktop')->src}
							<source media="(min-width: 1000px)" srcset="{$imageDesktop}">
							{var $imageTablet = $modal['cf']->intro_media->imgs->img_desktop->getSize('intro_article_tablet')->src}
							<source media="(min-width: 750px)" srcset="{$imageTablet}">
							{var $imageMobile = $modal['cf']->intro_media->imgs->img_desktop->getSize('intro_article_mobile')->src}
							<img src="{$imageMobile}" alt="" loading="lazy">
						</picture>
					</div>
				{elseif isset($modal['cf']->intro_media->file) && $modal['cf']->intro_media->file->url}
					{if isset($modal['cf']->intro_media->poster) && $modal['cf']->intro_media->poster->url}
						{var $poster = $modal['cf']->intro_media->poster->getSize('xxl')->src}
						<div class="b-custom__poster">
							<img src="{$poster}" alt="" loading="lazy">
						</div>
					{/if}
					<div class="b-custom__video">
						<video {if isset($modal['cf']->intro_media->autoplay) && $modal['cf']->intro_media->autoplay == true}autoplay loop muted playsinline{/if}>
							<source src="{$modal['cf']->intro_media->file->url}" type="video/mp4">
						</video>
					</div>
				{elseif isset($modal['cf']->intro_media->youtube) && $modal['cf']->intro_media->youtube}
					{if isset($modal['cf']->intro_media->poster) && $modal['cf']->intro_media->poster->url}
						{var $poster = $modal['cf']->intro_media->poster->getSize('xxl')->src}
						<div class="b-custom__poster">
							<img src="{$poster}" alt="" loading="lazy">
						</div>
					{/if}
					{var $videoUrl = $modal['cf']->intro_media->youtube}
					{var $videoId = preg_replace('#^.*v=([^&]+).*$#', '$1', $videoUrl)}
					<div class="b-custom__youtube">
						<iframe
							width="560"
							height="315"
							src="https://www.youtube.com/embed/{$videoId}?{if isset($modal['cf']->intro_media->autoplay) && $modal['cf']->intro_media->autoplay == true}autoplay=1&loop=1&playlist={$videoId}{/if}&mute=1&controls=0&modestbranding=1&rel=0"
							frameborder="0"
							allow="autoplay; encrypted-media"
							allowfullscreen>
						</iframe>
					</div>
				{/if}

				{if isset($modal['cf']) && isset($modal['cf']->title) && $modal['cf']->title}
					<div class="b-custom__title">
						<h2>
							{$modal['cf']->title}
						</h2>
					</div>
				{/if}

				{if isset($modal['cf']) && isset($modal['cf']->content) && $modal['cf']->content}
					<div class="b-custom__description">
						<p>
							{$modal['cf']->content}
						</p>
					</div>
				{/if}

				{if isset($modal['cf']) && isset($modal['cf']->link) && $modal['cf']->link}
					<div class="b-custom__button">
						{var $link = isset($modal['cf']->link->page) ? $presenter->link($modal['cf']->link->page) : ($modal['cf']->link->external ?? '#')}
						<a href="{$link}" class="btn btn--light btn--icon js-modal-close">
							<span class="btn__text">
								{$modal['cf']->link->text}
								{('bevel-right')|icon}
							</span>
						</a>
					</div>
				{/if}
			</div>
		</div>
	{/if}

{/block}
