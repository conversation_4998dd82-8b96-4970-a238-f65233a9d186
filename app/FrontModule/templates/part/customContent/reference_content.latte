{if isset($customContentItem->reference)}
	{default $title = isset($customContentItem->title) ? $customContentItem->title : false }
	{default $content = isset($customContentItem->content) ? $customContentItem->content : false }
	{default $reference = $customContentItem->reference??->getEntity() ?? false }
	{default $spacing = isset($customContentItem->spacing) ? $customContentItem->spacing : false }

	{include $templates.'/part/box/reference-bg.latte', title=>$title, content=>$content, reference=>$customContentItem->reference, spacing=>$spacing}
{/if}
