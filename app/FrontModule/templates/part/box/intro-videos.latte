{default $categories = false}
{default $cf = $object->cf->intro_hp??->carouselGroup ?? false}

{if $cf}
	{var $videos = $cf->videos ?? false}
	{var $autoplay = $cf??->autoplay ?? false}
	{var $poster = $cf??->poster ?? false}
	{var $loop = $object->cf->intro_hp??->loop ?? true}

	<header class="b-intro-videos embla u-mb-14" data-controller="Embla" {if $loop}data-Embla-settings-value='{"loop": true}'{/if}>
		<div class="b-intro-videos__wrapper embla__viewport" data-Embla-target="viewport" data-action="Video:timeupdate@window->Embla#updateProgress Video:ended@window->Embla#next">
			<ul n:if="$videos" class="b-intro-videos__list-main embla__container grid grid--x-0 grid--nowrap">
				<li n:foreach="$videos as $item" n:if="isset($item->link) && isset($item->text)" class="b-intro-videos__item-main embla__slide grid__cell grid__cell--eq{if $iterator->isFirst()} is-selected{/if}">
					<div class="b-intro-videos__bg img">
						<div class="b-intro-videos__video img">
							{if $autoplay}
								{include '../core/video.latte', type=>false, link=>$item->link, poster=>$poster, posterSize=>'xxl-16-9', dataAction=>"Embla:emblaChange@window->Video#stopOnSlides Embla:emblaChange@window->Video#autoplayOnSlide", dispatchTime=>true, autoplay=>$iterator->first ? 1 : 0, controls=>0}
							{else}
								{include '../core/video.latte', link=>$item->link, poster=>$poster, posterSize=>'xxl-16-9', dataAction=>"Embla:emblaChange@window->Video#stopOnSlides", autoplay=>false, controls=>1}
							{/if}
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="row-main">
			<button type="button" class="b-intro-videos__btn b-intro-videos__btn--prev embla__btn embla__btn--prev" data-action="Embla#prev" data-Embla-target="prevButton">
				<span class="u-vhide">
					{_"previous"}
				</span>
				{('angle-left')|icon}
			</button>
			<button type="button" class="b-intro-videos__btn b-intro-videos__btn--next embla__btn embla__btn--next" data-action="Embla#next" data-Embla-target="nextButton">
				<span class="u-vhide">
					{_"next"}
				</span>
				{('angle-right')|icon}
			</button>

			<!-- Thumbs -->
			<div class="b-intro-videos__thumbs embla">
				<div class="embla__viewport" data-Embla-target="thumbsViewport">
					<div class="b-intro-videos__list embla__container grid grid--nowrap grid--bottom grid--y-0 grid--x-2 grid--x-4@md">
						<div n:foreach="$videos as $item" n:if="isset($item->link) && isset($item->text)" class="b-intro-videos__item embla__slide grid__cell size--autogrow{if $iterator->isFirst()} is-selected{/if}">
							<button class="b-intro-videos__thumb" type="button">
								<p class="b-intro-videos__thumb-name">
									{$item->text}
									<i n:if="isset($item->textItalic)">{$item->textItalic}</i>
								</p>
								<div class="b-intro-videos__progress">
									<div class="b-intro-videos__progress-active" data-Embla-target="progress"></div>
								</div>
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
	</header>
{/if}
