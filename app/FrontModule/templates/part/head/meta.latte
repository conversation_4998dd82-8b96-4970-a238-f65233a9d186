{* Obrázek *}
{php $imgSrc = false}
{* {if $object->firstImage ?? false}
	{php $imgSrc = $object->firstImage->getSize('xl')->src} *}
{if $object instanceof App\Model\StaticPage\StaticPage}
	{php $imgSrc = false}
{elseif ($object->cf?->intro_hp?->imgs?->img_desktop ?? false) && $object->cf->intro_hp->imgs->img_desktop->getEntity()} {* HP intro *}
	{php $imgSrc = $object->cf->intro_hp->imgs->img_desktop->getSize('xl')->src}
{elseif ($object->cf?->intro_media?->imgs?->img_desktop ?? false) && $object->cf->intro_media->imgs->img_desktop->getEntity()} {* Intro *}
	{php $imgSrc = $object->cf->intro_media->imgs->img_desktop->getSize('xl')->src}
{elseif ($object->getParent()?->cf?->intro_media?->imgs?->img_desktop ?? false) && $object->getParent()->cf->intro_media->imgs->img_desktop->getEntity()} {* Posttype intro *}
	{php $imgSrc = $object->getParent()->cf->intro_media->imgs->img_desktop->getSize('xl')->src}
{elseif ($object->cf?->img_crossroad ?? false) && $object->cf->img_crossroad->getEntity()} {* Crossroad *}
	{php $imgSrc = $object->cf->img_crossroad->getSize('xl')->src}
{elseif ($object->getParent()?->cf?->img_crossroad ?? false) && $object->getParent()->cf->img_crossroad->getEntity()} {* Posttype crossroad *}
	{php $imgSrc = $object->getParent()->cf->img_crossroad->getSize('xl')->src}
{/if}

{* {dump $object->cf} *}
{* {dump $object->getParent()?->cf} *}

{* twitter *}
<meta name="twitter:card" content="summary">
{if isset($object->nameTitle)}
	<meta name="twitter:title" content="{$object->nameTitle}{if !$isHomepage} | {_title}{/if}">
{/if}

{if isset($object->description) && $object->description}
	<meta name="twitter:description" content="{$object->description}">
{elseif $object->cf->annotation ?? null}
	<meta name="twitter:description" content="{$object->cf->annotation}">
{elseif isset($object->annotation) && $object->annotation}
	<meta name="twitter:description" content="{$object->annotation|texy:true}">
{/if}

{if $object instanceof App\Model\StaticPage\StaticPage}
	<meta property="twitter:image" content="{$mutation->getBaseUrl()}/static/img/social/twitter.png">
{elseif $object->getParent()?->cf?->social_images?->tw ?? false}
	{php $img = $object->getParent()->cf->social_images->tw}
	{if $img instanceof App\Model\CustomField\LazyValue}
		{php $img = $object->getParent()->cf->social_images->tw->getEntity()}
	{/if}
	<meta n:if="$img" property="twitter:image" content="{$img->getSize('md')->src}">
{elseif $object->cf?->social_images?->tw ?? false}
	{php $img = $object->cf->social_images->tw}
	{if $img instanceof App\Model\CustomField\LazyValue}
		{php $img = $object->cf->social_images->tw->getEntity()}
	{/if}
	<meta n:if="$img" property="twitter:image" content="{$img->getSize('md')->src}">
{elseif $imgSrc}
	<meta property="twitter:image" content="{$imgSrc}">
{else}
	<meta property="twitter:image" content="{$mutation->getBaseUrl()}/static/img/social/twitter.png">
{/if}

{* facebook *}
{if isset($object->nameTitle)}
	<meta property="og:title" content="{$object->nameTitle}">
{/if}

{if isset($object->description) && $object->description}
	<meta property="og:description" content="{$object->description}">
{elseif $object->cf->annotation ?? null}
	<meta name="twitter:description" content="{$object->cf->annotation}">
{elseif isset($object->annotation) && $object->annotation}
	<meta property="og:description" content="{$object->annotation}">
{/if}

{if $object instanceof App\Model\StaticPage\StaticPage}
	<meta property="og:image" content="{$mutation->getBaseUrl()}/static/img/social/facebook.png">
{elseif $object->getParent()?->cf?->social_images?->fb ?? false}
	{php $img = $object->getParent()->cf->social_images->fb}
	{if $img instanceof App\Model\CustomField\LazyValue}
		{php $img = $object->getParent()->cf->social_images->fb->getEntity()}
	{/if}
	<meta n:if="$img" property="twitter:image" content="{$img->getSize('xl')->src}">
{elseif $object->cf?->social_images?->fb ?? false}
	{php $img = $object->cf->social_images->fb}
	{if $img instanceof App\Model\CustomField\LazyValue}
		{php $img = $object->cf->social_images->fb->getEntity()}
	{/if}
	<meta n:if="$img" property="twitter:image" content="{$img->getSize('xl')->src}">
{elseif $imgSrc}
	<meta property="og:image" content="{$imgSrc}">
{else}
	<meta property="og:image" content="{$mutation->getBaseUrl()}/static/img/social/facebook.png">
{/if}

<meta property="og:site_name" content="{_title}">

{if $object instanceof App\Model\StaticPage\StaticPage}
	<meta property="og:url" content="{$mutation->getBaseUrl()}">
{else}
	<meta property="og:url" content="{$mutation->getBaseUrl()}/{$object->alias}">
{/if}
{if isset($object) && $object && isset($object->template) && $object->template == 'Product:detail'}
	<meta property="og:type" content="product">
{elseif isset($object) && $object && isset($object->template) && $object->template == 'Blog:detail'}
	<meta property="og:type" content="article">
{else}
	<meta property="og:type" content="website">
{/if}

{* favicons *}
<link rel="apple-touch-icon" sizes="180x180" href="{$mutation->getBaseUrl()}/static/img/favicon/apple-touch-icon.png">
<link rel="icon" type="image/png" sizes="32x32" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon-32x32.png">
<link rel="icon" type="image/png" sizes="16x16" href="{$mutation->getBaseUrl()}/static/img/favicon/favicon-16x16.png">
{* <link rel="manifest" href="{$mutation->getBaseUrl()}/static/img/favicon/site.webmanifest"> *}
<link rel="mask-icon" href="{$mutation->getBaseUrl()}/static/img/favicon/safari-pinned-tab.svg" color="#0d0d0d">
<meta name="msapplication-TileColor" content="#0d0d0d">
<meta name="theme-color" content="#ffffff">
