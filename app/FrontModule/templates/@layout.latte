<!DOCTYPE html>
<html lang="{$mutation->langCode}" class="no-js">
	<head>
		<meta charset="utf-8">
		<!--[if IE]><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><![endif]-->
		{var $keywords = $seoLink??->keywords ?? $object->keywords}
		{if $keywords !== null}
			<meta name="keywords" content="{$keywords}">
		{/if}

		{var $description = $seoLink??->description ?? $object->description ?? $object->annotation}
		{if $description !== null}
			<meta name="description" content="{$description|texy:true}">
		{/if}

		{control robots}
		{control canonicalUrl}
		<meta name="viewport" content="width=device-width, initial-scale=1">

		<title n:snippet="title">
				{if isset($seoLink) && $seoLink && $seoLink->nameTitle}
					{$seoLink->nameTitle}
				{elseif !empty($seoFilterCatalog->title)} {* H1 po filtraci *}
					{$seoFilterCatalog->title}
				{elseif isset($object->nameTitle)}
					{$object->nameTitle}
				{/if}
			{if !$isHomepage} {* Přídavek za title, který se dává jen pro ne homepage stránky *}
					| {_title}
				{/if}
		</title>

		{include 'part/head/style.latte', object=>$object}
		{include 'part/head/scripts.latte', object=>$object}

		<link n:foreach="$hrefLangs as $hrefLang => $href" rel="alternate" hreflang="{$hrefLang}" href="{$href}"/>

		{include 'part/head/meta.latte'}
		{include 'part/head/structured_data.latte'}

		{include 'part/tracking/googleAnalytics.latte' showTop=>TRUE}

		{var $scripts = [
			'/static/js/app.js?t=' . $webVersion
		] }
		{foreach $scripts as $script}
			<link rel="preload" as="script" href="{$script}">
		{/foreach}
		{*
			<link rel="dns-prefetch" href="https://www.google-analytics.com">
			<link rel="dns-prefetch" href="https://www.googletagmanager.com"> {/gtm.js}
			<link rel="preconnect" href="https://www.google.com" crossorigin>
			<link rel="preconnect" href="https://www.youtube.com" crossorigin>
			<link rel="preconnect" href="https://connect.facebook.net" crossorigin>
			<link rel="preconnect" href="https://static.doubleclick.net" crossorigin>
			<link rel="preconnect" href="https://client.crisp.chat" crossorigin>
		*}
	</head>
	{php $colorTheme = $object->cf->colorTheme->colorTheme ?? false}

	{php $defaultLightHeader = in_array($object->template, [':Front:Homepage:default', ':Blog:Front:Blog:detail', 'Author:detail', 'Designer:detail', 'Event:detail', ':Front:Page:signature']) || (isset($object->uid) && in_array($object->uid, ['404', 'aboutUs']))}
	{php $cfLightHeader = $object->cf->inverted_header??->inverted ?? 'automatic'}
	{php $lightHeader = $cfLightHeader != 'automatic' ? $cfLightHeader == 'true' : $defaultLightHeader}

	<body data-controller="Naja Modal" n:class="$lightHeader == true ? light-header, $colorTheme ? 'theme-'.$colorTheme">
		{include 'part/tracking/googleAnalytics.latte' showBottom=>TRUE}
		{include 'part/menu/accessibility.latte'}

		{snippetArea header}
			{include 'part/header.latte'}
		{/snippetArea}

		{snippet contentModal}
			{control modal}
			<main id="main" class="main">
				{include #content}
			</main>
		{/snippet}

		{* {include './part/box/last-visited.latte'} *}

		{snippet footer}
			{include 'part/footer.latte'}
		{/snippet}
		{include 'part/box/cookie.latte'}

		<div class="body-loader__loader"></div>

{*TODO new version*}
{*		{include 'part/tracking/gtm_end.latte'}*}

		{control editButton}
		<div class="body-loader__loader"></div>

		{* Přehrání lite-youtube, lite-vimeo *}
		<script>
			document.addEventListener('click', function(event) {
				if (event.target.classList.contains('lty-playbtn') || event.target.classList.contains('ltv-playbtn') || event.target.classList.contains('ltv-activated')) {
					dataLayer.push({
						'event' : 'VIDEO_PLAY'
					});
				}
			});
		</script>

		{foreach $scripts as $script}
			<script src="{$script}"></script>
		{/foreach}
		<script>
			App.run({
				apiKey: {$googleApiKey},
				assetsUrl: '/static/',
			});
		</script>
	</body>
</html>
